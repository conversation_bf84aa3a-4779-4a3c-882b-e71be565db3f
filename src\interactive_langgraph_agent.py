#!/usr/bin/env python3
"""
交互式LangGraph智能体 - 集成Human-in-the-Loop工具选择
"""

import asyncio
import time
import logging
from typing import Dict, Any, List, Optional
try:
    from typing import Literal
except ImportError:
    from typing_extensions import Literal
from dataclasses import dataclass

try:
    from typing import TypedDict, Annotated
except ImportError:
    from typing_extensions import TypedDict, Annotated

from langgraph.graph import StateGraph, END
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage

# 导入现有组件
try:
    from .mcp_client import MCPClient, WindTurbineDBClient
    from .multi_api_client import MultiAPIClient
    from .config import get_config
    from .enhanced_intelligent_router import EnhancedIntelligentRouter
    from .mcp_tools_client_fixed import MCPToolsClientFixed
    # Phase 6: 导入混合语义分析系统和智能记忆管理器
    from .memory_enhancement.hybrid_semantic_analyzer import AdaptiveSemanticRouter
    from .memory_enhancement.intelligent_memory_manager import IntelligentMemoryManager
    SEMANTIC_ENHANCEMENT_AVAILABLE = True
except ImportError:
    from mcp_client import MC<PERSON>lient, Wind<PERSON><PERSON><PERSON>D<PERSON><PERSON>
    from multi_api_client import MultiAPIClient
    from config import get_config
    from enhanced_intelligent_router import EnhancedIntelligentRouter
    from mcp_tools_client_fixed import MCPToolsClientFixed
    # Phase 6: 导入混合语义分析系统和智能记忆管理器
    try:
        from memory_enhancement.hybrid_semantic_analyzer import AdaptiveSemanticRouter
        from memory_enhancement.intelligent_memory_manager import IntelligentMemoryManager
        SEMANTIC_ENHANCEMENT_AVAILABLE = True
    except ImportError:
        SEMANTIC_ENHANCEMENT_AVAILABLE = False
        print("⚠️ 混合语义分析系统未找到，使用基础功能")

# 导入内容结构化解析器 - 范式转移支持
try:
    import sys
    import os
    # 添加web后端路径到sys.path
    web_backend_path = os.path.join(os.path.dirname(__file__), '..', 'web_turbine_interface', 'turbine_backend', 'app')
    if web_backend_path not in sys.path:
        sys.path.append(web_backend_path)
    from services.content_structure_parser import ContentStructureParser
    CONTENT_PARSER_AVAILABLE = True
    print("✅ 内容结构化解析器加载成功")
except ImportError as e:
    CONTENT_PARSER_AVAILABLE = False
    print(f"⚠️ 内容结构化解析器未找到: {e}")
    print("将使用传统Markdown输出格式")

logger = logging.getLogger(__name__)

@dataclass
class ToolOption:
    """工具选项"""
    name: str
    description: str
    estimated_time: float
    confidence: float
    reason: str

class InteractiveAgentState(TypedDict):
    """交互式智能体状态"""
    # 基础信息
    user_query: str
    messages: Annotated[List[BaseMessage], "消息历史"]

    # 分析结果
    query_analysis: Dict[str, Any]
    available_tools: List[ToolOption]
    selected_tools: List[str]

    # 数据收集
    local_data: Dict[str, Any]
    online_data: Dict[str, Any]
    tech_docs: Dict[str, Any]
    file_data: Dict[str, Any]

    # 用户交互
    user_choice: Optional[str]
    needs_confirmation: bool
    interaction_history: List[Dict[str, Any]]

    # Phase 7 Week 2: 流式AI回答支持
    stream_mode: bool

    # 最终结果
    final_response: str
    execution_time: float
    error: Optional[str]

class InteractiveLangGraphAgent:
    """交互式LangGraph智能体"""
    
    def __init__(self, config=None):
        """初始化交互式智能体"""
        self.config = config if config is not None else get_config()
        
        # 初始化组件
        self.mcp_client = MCPClient()
        self.db_client = WindTurbineDBClient(self.mcp_client)
        self.ai_client = MultiAPIClient(config)  # 使用多API客户端
        self.router = EnhancedIntelligentRouter()
        self.mcp_tools = MCPToolsClientFixed()

        # Phase 6: 初始化混合语义分析系统和智能记忆管理器
        if SEMANTIC_ENHANCEMENT_AVAILABLE:
            self.semantic_router = AdaptiveSemanticRouter()
            self.memory_manager = IntelligentMemoryManager("wind_turbine_memory.json")
            print("✅ 混合语义分析系统已启用")
            print("✅ 智能记忆管理器已启用")
        else:
            self.semantic_router = None
            self.memory_manager = None
        
        # 工具配置
        self.available_tools = {
            "local-knowledge-base": {
                "name": "本地知识库",
                "description": "搜索本地PDF知识库，包含14个风机技术文档",
                "estimated_time": 0.1,
                "best_for": ["风机技术", "NREL研究", "控制设计", "故障分析", "维护指南"]
            },
            "wind-turbine-db": {
                "name": "风机数据库",
                "description": "查询风机故障、组件信息和历史记录",
                "estimated_time": 0.5,
                "best_for": ["故障查询", "组件信息", "历史数据"]
            },
            "context7": {
                "name": "技术文档",
                "description": "搜索技术文档、操作手册和最佳实践",
                "estimated_time": 2.0,
                "best_for": ["技术原理", "操作指南", "设计规范"]
            },
            "fetch": {
                "name": "在线搜索",
                "description": "获取最新的在线信息和技术资料",
                "estimated_time": 3.0,
                "best_for": ["最新信息", "行业动态", "技术趋势"]
            },
            "pdf-processor": {
                "name": "PDF文档",
                "description": "处理和搜索PDF技术手册",
                "estimated_time": 1.5,
                "best_for": ["手册查询", "规范检索", "文档分析"]
            },
            "filesystem": {
                "name": "文件系统",
                "description": "读取、分析本地文件和日志",
                "estimated_time": 0.3,
                "best_for": ["文件读取", "日志分析", "数据处理"]
            }
        }
        
        # 构建交互式状态图
        self.graph = self._build_interactive_graph()
        
        logger.info("交互式LangGraph智能体初始化完成")
    
    def _build_interactive_graph(self) -> StateGraph:
        """构建交互式状态图"""
        workflow = StateGraph(InteractiveAgentState)
        
        # 添加节点
        workflow.add_node("analyze_query", self._analyze_query_node)
        workflow.add_node("suggest_tools", self._suggest_tools_node)
        workflow.add_node("wait_user_choice", self._wait_user_choice_node)
        workflow.add_node("execute_tools", self._execute_tools_node)
        workflow.add_node("process_with_ai", self._process_with_ai_node)
        workflow.add_node("generate_response", self._generate_response_node)
        
        # 设置入口点
        workflow.set_entry_point("analyze_query")
        
        # 添加边
        workflow.add_edge("analyze_query", "suggest_tools")
        workflow.add_edge("suggest_tools", "wait_user_choice")
        
        # 条件边：用户选择后的路由
        workflow.add_conditional_edges(
            "wait_user_choice",
            self._route_after_user_choice,
            {
                "execute_tools": "execute_tools",
                "direct_ai": "process_with_ai",
                "wait_more": "wait_user_choice"
            }
        )
        
        workflow.add_edge("execute_tools", "process_with_ai")
        workflow.add_edge("process_with_ai", "generate_response")
        workflow.add_edge("generate_response", END)
        
        return workflow.compile()
    
    async def _analyze_query_node(self, state: InteractiveAgentState) -> InteractiveAgentState:
        """分析查询节点 - Phase 6 语义增强 + Week 4 记忆检索"""
        print("🔍 分析用户查询...")

        analysis = self.router.analyze_query_intent(state["user_query"])

        # Phase 6: 混合语义分析
        semantic_result = None
        if self.semantic_router:
            # 将新格式转换为旧格式兼容
            query_type = analysis.primary_intent if hasattr(analysis, 'primary_intent') else "SIMPLE_FACT"
            extracted_entities = {}  # 新路由器暂时不提供实体提取

            semantic_result = self.semantic_router.analyze_with_routing(
                state["user_query"],
                query_type,
                extracted_entities
            )
            print(f"🧠 语义分析: {semantic_result.core_concept} | {semantic_result.technical_domain} | {semantic_result.intent_depth} | {semantic_result.analysis_method}")
            if semantic_result.confidence_score > 0:
                print(f"📊 置信度: {semantic_result.confidence_score:.2f} | ⏱️ 处理时间: {semantic_result.processing_time:.4f}秒")

        # Phase 6 Week 4: 智能记忆检索增强
        relevant_memories = []
        personalized_recommendations = {}
        if self.memory_manager and semantic_result:
            try:
                # 检索相关历史记忆
                relevant_memories = self.memory_manager.retrieve_relevant_memories(semantic_result, limit=3)
                if relevant_memories:
                    print(f"💭 检索到 {len(relevant_memories)} 条相关记忆")
                    for i, memory in enumerate(relevant_memories, 1):
                        print(f"   {i}. {memory.semantic_result.get('core_concept', '未知概念')} | 质量: {memory.quality_score:.2f} | 访问: {memory.access_count}次")

                # 获取个性化推荐
                personalized_recommendations = self.memory_manager.get_personalized_recommendations(semantic_result)
                if personalized_recommendations.get("complexity_advice"):
                    print(f"💡 个性化建议: {personalized_recommendations['complexity_advice']}")

            except Exception as e:
                print(f"⚠️ 记忆检索失败: {e}")

        state["query_analysis"] = {
            "query_type": analysis.primary_intent,
            "confidence": analysis.confidence,
            "entities": {},  # 新路由器暂时不提供实体提取
            "reasoning": analysis.reasoning,
            "semantic_result": semantic_result,  # Phase 6: 添加混合语义分析结果
            "relevant_memories": relevant_memories,  # Week 4: 相关记忆
            "personalized_recommendations": personalized_recommendations  # Week 4: 个性化推荐
        }

        print(f"   查询类型: {analysis.primary_intent}")
        print(f"   置信度: {analysis.confidence:.2f}")

        return state
    
    async def _suggest_tools_node(self, state: InteractiveAgentState) -> InteractiveAgentState:
        """建议工具节点 - Week 4 个性化推荐增强"""
        print("🛠️ 分析可用工具...")

        query = state["user_query"]  # 保持原始大小写用于路径匹配
        query_lower = query.lower()  # 小写版本用于关键词匹配
        analysis = state["query_analysis"]

        # 智能推荐工具
        tool_suggestions = []

        # Phase 6 Week 4: 基于个性化推荐的工具建议
        personalized_recommendations = analysis.get("personalized_recommendations", {})
        relevant_memories = analysis.get("relevant_memories", [])

        # 显示个性化建议 - Week 4 增强版
        if personalized_recommendations:
            suggested_tools = personalized_recommendations.get("suggested_tools", [])
            if suggested_tools:
                print(f"💡 基于您的使用习惯推荐: {', '.join(suggested_tools[:3])}")

            related_domains = personalized_recommendations.get("related_domains", [])
            if related_domains:
                print(f"🔗 相关技术领域: {', '.join(related_domains[:2])}")

            # 显示主动建议
            proactive_suggestions = personalized_recommendations.get("proactive_suggestions", [])
            if proactive_suggestions:
                print(f"🚀 主动建议: {proactive_suggestions[0]}")

            # 显示上下文洞察
            context_insights = personalized_recommendations.get("context_insights", [])
            if context_insights:
                print(f"🔍 上下文洞察: {context_insights[0]}")

            # 显示学习资源推荐
            learning_resources = personalized_recommendations.get("learning_resources", [])
            if learning_resources:
                print(f"📚 学习建议: {learning_resources[0]}")

        # 基于相关记忆的工具推荐
        memory_suggested_tools = set()
        if relevant_memories:
            for memory in relevant_memories:
                memory_tools = memory.interaction_context.get("tools_used", [])
                memory_suggested_tools.update(memory_tools)

            if memory_suggested_tools:
                print(f"📚 历史成功经验推荐: {', '.join(list(memory_suggested_tools)[:3])}")

        # 使用增强智能路由器获取工具推荐
        router_intent = self.router.analyze_query_intent(query)

        # 将路由器推荐转换为工具建议
        for tool_name in router_intent.required_tools:
            if tool_name in self.available_tools:
                tool_info = self.available_tools[tool_name]
                tool_suggestions.append(ToolOption(
                    name=tool_name,
                    description=tool_info["description"],
                    estimated_time=tool_info["estimated_time"],
                    confidence=router_intent.confidence,
                    reason=f"智能路由器推荐 (必需工具)"
                ))

        for tool_name in router_intent.optional_tools:
            if tool_name in self.available_tools:
                tool_info = self.available_tools[tool_name]
                tool_suggestions.append(ToolOption(
                    name=tool_name,
                    description=tool_info["description"],
                    estimated_time=tool_info["estimated_time"],
                    confidence=router_intent.confidence * 0.8,  # 可选工具置信度稍低
                    reason=f"智能路由器推荐 (可选工具)"
                ))

        # 基于查询内容的补充推荐
        import re

        # 优先检查文件系统操作
        file_path_pattern = r'[A-Za-z]:\\[^\\/:*?"<>|]+(?:\\[^\\/:*?"<>|]+)*\.[a-zA-Z0-9]+'
        if re.search(file_path_pattern, query) and any(word in query_lower for word in ["读取", "打开", "分析", "统计", "处理"]):
            if not any(tool.name == "filesystem" for tool in tool_suggestions):
                tool_suggestions.append(ToolOption(
                    name="filesystem",
                    description="读取和分析指定的文件内容",
                    estimated_time=0.3,
                    confidence=0.95,
                    reason="查询包含文件路径和文件操作"
                ))

        # 如果没有推荐任何工具，使用默认推荐
        if not tool_suggestions:
            if any(word in query_lower for word in ["故障", "问题", "异常"]):
                tool_suggestions.append(ToolOption(
                    name="wind-turbine-db",
                    description="查询相关故障记录和解决方案",
                    estimated_time=0.5,
                    confidence=0.7,
                    reason="查询包含故障相关关键词"
                ))

        if any(word in query_lower for word in ["如何", "怎么", "方法", "步骤"]) and not tool_suggestions:
            tool_suggestions.append(ToolOption(
                name="context7",
                description="搜索技术操作指南和最佳实践",
                estimated_time=2.0,
                confidence=0.8,
                reason="查询需要技术指导"
            ))

        if any(word in query_lower for word in ["最新", "搜索", "联网", "在线"]):
            tool_suggestions.append(ToolOption(
                name="fetch",
                description="获取最新的在线技术信息",
                estimated_time=3.0,
                confidence=0.7,
                reason="查询需要最新信息"
            ))

        if any(word in query_lower for word in ["手册", "文档", "规范"]):
            tool_suggestions.append(ToolOption(
                name="pdf-processor",
                description="搜索PDF技术手册和规范文档",
                estimated_time=1.5,
                confidence=0.6,
                reason="查询涉及文档资料"
            ))
        
        # 如果没有明确的工具推荐，提供通用选项
        if not tool_suggestions:
            tool_suggestions.extend([
                ToolOption(
                    name="wind-turbine-db",
                    description="查询风机基础信息",
                    estimated_time=0.5,
                    confidence=0.5,
                    reason="通用数据库查询"
                ),
                ToolOption(
                    name="context7",
                    description="搜索技术文档",
                    estimated_time=2.0,
                    confidence=0.5,
                    reason="通用技术查询"
                )
            ])

        # Phase 6 Week 4: 基于个性化推荐调整工具优先级
        if personalized_recommendations:
            suggested_tools = personalized_recommendations.get("suggested_tools", [])

            # 提升个性化推荐工具的置信度
            for tool in tool_suggestions:
                if tool.name in suggested_tools:
                    tool.confidence = min(tool.confidence + 0.2, 1.0)
                    tool.reason += " (个性化推荐)"

                # 基于历史成功经验提升置信度
                if tool.name in memory_suggested_tools:
                    tool.confidence = min(tool.confidence + 0.15, 1.0)
                    tool.reason += " (历史成功经验)"

            # 按置信度排序
            tool_suggestions.sort(key=lambda x: x.confidence, reverse=True)

        state["available_tools"] = tool_suggestions
        state["needs_confirmation"] = True

        return state
    
    async def _wait_user_choice_node(self, state: InteractiveAgentState) -> InteractiveAgentState:
        """等待用户选择节点"""
        if not state["needs_confirmation"]:
            return state
        
        print("\n" + "="*60)
        print("🤔 请选择您希望使用的数据源:")
        print("="*60)
        
        # 显示工具选项
        for i, tool in enumerate(state["available_tools"], 1):
            tool_info = self.available_tools.get(tool.name, {})
            print(f"\n{i}. 【{tool_info.get('name', tool.name)}】")
            print(f"   描述: {tool.description}")
            print(f"   预计时间: {tool.estimated_time}秒")
            print(f"   推荐理由: {tool.reason}")
            print(f"   置信度: {tool.confidence:.1f}")
        
        print(f"\n{len(state['available_tools']) + 1}. 【仅使用AI分析】")
        print("   描述: 直接使用AI进行分析，不查询额外数据")
        print("   预计时间: 5秒")
        
        print(f"\n{len(state['available_tools']) + 2}. 【使用所有推荐工具】")
        print("   描述: 使用所有推荐的数据源进行综合分析")
        total_time = sum(tool.estimated_time for tool in state["available_tools"]) + 5
        print(f"   预计时间: {total_time}秒")

        print(f"\n{len(state['available_tools']) + 3}. 【查看所有可用MCP工具】")
        print("   描述: 显示所有可用的MCP工具，手动选择组合")
        print("   预计时间: 根据选择而定")

        print("\n" + "="*60)
        
        # 获取用户输入
        try:
            choice = input("请输入选项编号 (1-{}): ".format(len(state["available_tools"]) + 3)).strip()
            
            if choice.isdigit():
                choice_num = int(choice)
                if 1 <= choice_num <= len(state["available_tools"]):
                    # 选择单个工具
                    selected_tool = state["available_tools"][choice_num - 1]
                    state["selected_tools"] = [selected_tool.name]
                    print(f"✅ 已选择: {self.available_tools[selected_tool.name]['name']}")
                    
                elif choice_num == len(state["available_tools"]) + 1:
                    # 仅使用AI
                    state["selected_tools"] = []
                    print("✅ 已选择: 仅使用AI分析")
                    
                elif choice_num == len(state["available_tools"]) + 2:
                    # 使用所有工具
                    state["selected_tools"] = [tool.name for tool in state["available_tools"]]
                    print("✅ 已选择: 使用所有推荐工具")

                elif choice_num == len(state["available_tools"]) + 3:
                    # 显示所有可用MCP工具
                    selected_tools = self._show_all_mcp_tools()
                    if selected_tools:
                        state["selected_tools"] = selected_tools
                        print(f"✅ 已选择: {len(selected_tools)}个MCP工具")
                    else:
                        state["selected_tools"] = []
                        print("✅ 已选择: 仅使用AI分析")

                else:
                    print("❌ 无效选择，将使用AI分析")
                    state["selected_tools"] = []
            else:
                print("❌ 无效输入，将使用AI分析")
                state["selected_tools"] = []
                
        except (KeyboardInterrupt, EOFError):
            print("\n⏸️ 用户中断，使用默认选择")
            state["selected_tools"] = [state["available_tools"][0].name] if state["available_tools"] else []
        
        state["user_choice"] = choice if 'choice' in locals() else "default"
        state["needs_confirmation"] = False

        # Phase 7 Week 2: 询问是否启用流式AI回答
        try:
            print("\n" + "-" * 60)
            print("🌊 AI回答模式选择:")
            print("1. 流式回答 (实时显示，类似ChatGPT)")
            print("2. 普通回答 (等待完成后显示)")

            stream_choice = input("请选择回答模式 (1-2，默认1): ").strip()

            if stream_choice == "2":
                state["stream_mode"] = False
                print("✅ 已选择: 普通回答模式")
            else:
                state["stream_mode"] = True
                print("✅ 已选择: 流式回答模式")

        except (KeyboardInterrupt, EOFError):
            state["stream_mode"] = True  # 默认启用流式
            print("\n✅ 默认选择: 流式回答模式")

        return state

    def _show_all_mcp_tools(self):
        """显示所有可用的MCP工具供用户选择"""
        print("\n" + "="*70)
        print("🛠️  所有可用的MCP工具")
        print("="*70)

        # 定义所有可用的MCP工具
        all_mcp_tools = {
            "local-knowledge-base": {
                "name": "本地知识库",
                "description": "搜索本地PDF知识库，包含14个风机技术文档",
                "best_for": "风机技术、NREL研究、控制设计、故障分析、维护指南",
                "time": 0.1
            },
            "wind-turbine-db": {
                "name": "风机数据库",
                "description": "查询风机故障、组件信息、维护记录",
                "best_for": "故障诊断、组件查询、历史数据",
                "time": 0.5
            },
            "context7": {
                "name": "技术文档库",
                "description": "查询技术手册、设计规范、操作指南",
                "best_for": "技术原理、操作步骤、设计标准",
                "time": 2.0
            },
            "fetch": {
                "name": "在线搜索",
                "description": "获取最新的在线技术信息和产品资料",
                "best_for": "最新信息、产品规格、行业动态",
                "time": 3.0
            },
            "pdf-processor": {
                "name": "PDF文档处理",
                "description": "处理和搜索PDF技术手册、规范文档",
                "best_for": "文档解析、手册查询、规范检索",
                "time": 1.5
            },
            "filesystem": {
                "name": "文件系统",
                "description": "读取、创建、管理项目文件和目录",
                "best_for": "文件操作、代码查看、配置管理",
                "time": 0.3
            }
        }

        # 显示工具列表
        for i, (tool_id, tool_info) in enumerate(all_mcp_tools.items(), 1):
            print(f"\n{i}. 【{tool_info['name']}】({tool_id})")
            print(f"   📝 功能: {tool_info['description']}")
            print(f"   🎯 适用: {tool_info['best_for']}")
            print(f"   ⏱️  时间: ~{tool_info['time']}秒")

        print(f"\n{len(all_mcp_tools) + 1}. 【完成选择】")
        print("   📋 确认当前选择并继续")

        print("\n" + "="*70)
        print("💡 提示: 可以输入多个编号(用空格分隔)，如: 1 2 3")
        print("💡 提示: 输入 0 取消选择，直接使用AI分析")

        selected_tools = []
        tool_list = list(all_mcp_tools.keys())

        while True:
            try:
                user_input = input(f"\n请选择工具编号 (1-{len(all_mcp_tools)}) 或输入 0 取消: ").strip()

                if user_input == "0":
                    print("✅ 已取消，将仅使用AI分析")
                    return []

                if user_input == str(len(all_mcp_tools) + 1) or user_input.lower() in ['done', '完成']:
                    break

                # 解析用户输入的编号
                choices = []
                for choice in user_input.split():
                    if choice.isdigit():
                        choice_num = int(choice)
                        if 1 <= choice_num <= len(all_mcp_tools):
                            tool_id = tool_list[choice_num - 1]
                            if tool_id not in selected_tools:
                                selected_tools.append(tool_id)
                                tool_name = all_mcp_tools[tool_id]['name']
                                print(f"  ✅ 已添加: {tool_name}")
                            else:
                                print(f"  ⚠️  已存在: {all_mcp_tools[tool_id]['name']}")
                        else:
                            print(f"  ❌ 无效编号: {choice}")
                    else:
                        print(f"  ❌ 无效输入: {choice}")

                if selected_tools:
                    print(f"\n📋 当前已选择 {len(selected_tools)} 个工具:")
                    for tool_id in selected_tools:
                        print(f"   • {all_mcp_tools[tool_id]['name']}")
                    print(f"\n继续选择更多工具，或输入 {len(all_mcp_tools) + 1} 完成选择")
                else:
                    print("\n📋 当前未选择任何工具")

            except KeyboardInterrupt:
                print("\n\n✅ 已取消选择")
                return []
            except Exception as e:
                print(f"❌ 输入错误: {e}")

        if selected_tools:
            total_time = sum(all_mcp_tools[tool]['time'] for tool in selected_tools)
            print(f"\n🎯 最终选择: {len(selected_tools)} 个工具")
            for tool_id in selected_tools:
                print(f"   • {all_mcp_tools[tool_id]['name']}")
            print(f"⏱️  预计总时间: ~{total_time:.1f}秒")

        return selected_tools

    def _generate_search_url(self, query: str) -> str:
        """根据查询内容生成搜索URL"""
        query_lower = query.lower()

        # 简单的关键词匹配
        if "美孚" in query or "mobil" in query_lower:
            return "https://www.mobil.com/en/industrial/lubricants/gear-oils"
        elif "壳牌" in query or "shell" in query_lower:
            return "https://www.shell.com/business-customers/lubricants-for-business/industrial-lubricants/gear-oils.html"
        elif "风机" in query or "wind" in query_lower:
            return "https://gwec.net/"
        else:
            # 使用Google搜索
            keywords = query.replace(" ", "+")
            return f"https://www.google.com/search?q={keywords}"

    def _extract_keywords(self, query: str) -> List[str]:
        """从查询中提取关键词"""
        import re
        # 移除常见停用词
        stop_words = {"的", "是", "在", "有", "和", "与", "或", "但", "如何", "什么", "哪些", "怎么", "为什么"}

        # 提取中英文词汇
        words = re.findall(r'[\u4e00-\u9fff]+|[a-zA-Z]+', query)
        keywords = [word for word in words if len(word) > 1 and word not in stop_words]

        return keywords

    def _generate_fallback_suggestions(self, query: str) -> Dict[str, str]:
        """生成备用建议"""
        suggestions = [
            "建议手动访问相关官方网站",
            "尝试使用更具体的关键词搜索",
            "查询专业技术数据库",
            "联系相关技术专家或供应商"
        ]

        return {
            "content": f"在线搜索暂时无法访问，建议采用以下方式获取信息：\n" +
                      "\n".join(f"• {suggestion}" for suggestion in suggestions),
            "source": "备用建议",
            "suggestions": suggestions
        }
    
    def _route_after_user_choice(self, state: InteractiveAgentState) -> Literal["execute_tools", "direct_ai", "wait_more"]:
        """用户选择后的路由"""
        if state["needs_confirmation"]:
            return "wait_more"
        elif state["selected_tools"]:
            return "execute_tools"
        else:
            return "direct_ai"
    
    async def _execute_tools_node(self, state: InteractiveAgentState) -> InteractiveAgentState:
        """执行工具节点"""
        print(f"\n🔧 执行选定的工具: {state['selected_tools']}")
        
        for tool_name in state["selected_tools"]:
            if tool_name not in self.available_tools:
                print(f"   ❌ 未知工具: {tool_name}")
                continue

            print(f"   正在执行: {self.available_tools[tool_name]['name']}...")

            try:
                if tool_name == "wind-turbine-db":
                    # 执行数据库查询
                    result = await self._query_database(state["user_query"])
                    state["local_data"] = result
                    
                elif tool_name == "local-knowledge-base":
                    # 执行本地PDF知识库查询
                    try:
                        kb_result = self.mcp_tools.call_mcp_tool("local-knowledge-base", "search_local_knowledge", {
                            "query": state["user_query"],
                            "max_results": 5
                        })

                        if kb_result and kb_result.get("success"):
                            # 格式化本地知识库结果
                            results = kb_result.get("results", [])
                            if results:
                                # 合并所有相关内容
                                combined_content = []
                                for result in results[:3]:  # 取前3个最相关的结果
                                    source = result.get("source_document", "未知来源")
                                    content = result.get("content", "")
                                    score = result.get("relevance_score", 0)
                                    combined_content.append(f"[来源: {source}, 相关性: {score:.2f}]\n{content}")

                                final_content = "\n\n---\n\n".join(combined_content)
                                state["tech_docs"] = {"content": final_content}
                                print(f"   ✅ 获取到本地知识库内容: {len(final_content)} 字符")
                                print(f"   📊 找到 {kb_result.get('total_found', 0)} 个相关结果")
                            else:
                                state["tech_docs"] = {"content": "未找到相关的本地文档内容"}
                                print("   ⚠️ 本地知识库未找到相关内容")
                        else:
                            error_msg = kb_result.get("error", "本地知识库查询失败") if kb_result else "本地知识库无响应"
                            state["tech_docs"] = {"error": error_msg}
                            print(f"   ❌ 本地知识库查询失败: {error_msg}")
                    except Exception as e:
                        print(f"   ❌ 本地知识库查询异常: {e}")
                        state["tech_docs"] = {"error": str(e)}

                elif tool_name == "context7":
                    # 执行技术文档查询
                    try:
                        # 使用Python库作为通用技术参考
                        docs_result = self.mcp_tools.call_mcp_tool("context7", "get-library-docs", {
                            "context7CompatibleLibraryID": "/context7/python-3",
                            "topic": state["user_query"],
                            "tokens": 5000
                        })

                        if docs_result and "content" in docs_result:
                            # 提取文档内容
                            content = docs_result["content"]
                            if isinstance(content, list) and len(content) > 0:
                                doc_text = content[0].get("text", "")
                                state["tech_docs"] = {"content": doc_text}
                                print(f"   ✅ 获取到技术文档: {len(doc_text)} 字符")
                            else:
                                state["tech_docs"] = {"content": str(content)}
                                print(f"   ✅ 获取到技术文档: {len(str(content))} 字符")
                        else:
                            print("   ⚠️ 技术文档格式异常")
                            state["tech_docs"] = {"error": "技术文档格式异常"}
                    except Exception as e:
                        print(f"   ⚠️ 技术文档查询失败: {e}")
                        state["tech_docs"] = {"error": str(e)}
                    
                elif tool_name == "filesystem":
                    # 执行文件系统操作
                    try:
                        # 从查询中提取文件路径
                        import re, os
                        file_path_pattern = r'[A-Za-z]:\\[^\\/:*?"<>|]+(?:\\[^\\/:*?"<>|]+)*\.[a-zA-Z0-9]+'
                        file_path_match = re.search(file_path_pattern, state["user_query"])

                        if file_path_match:
                            file_path = file_path_match.group()
                            print(f"   尝试读取文件: {file_path}")

                            # 调用filesystem工具
                            result = self.mcp_tools.call_mcp_tool("filesystem", "read_file", {
                                "path": file_path
                            })

                            if result and not result.get('isError', False):
                                state["file_data"] = result
                                print(f"   ✅ 文件读取成功: {len(str(result))} 字符")
                            else:
                                # 处理权限错误，提供友好的解决方案
                                error_msg = str(result) if result else "文件读取失败或文件不存在"

                                # 检查是否是权限问题
                                if "Access denied" in error_msg or "outside allowed directories" in error_msg:
                                    # 提取文件名
                                    file_name = os.path.basename(file_path)
                                    suggested_path = f"E:\\风机智能体测试\\local_hybrid_agent\\{file_name}"

                                    enhanced_error = {
                                        "error": "文件访问权限受限",
                                        "original_path": file_path,
                                        "suggested_path": suggested_path,
                                        "solution": f"请将文件复制到以下路径后重试：\n{suggested_path}",
                                        "reason": "系统仅允许访问项目目录内的文件，这是为了确保安全性"
                                    }
                                    state["file_data"] = enhanced_error
                                    print(f"   ⚠️ 文件访问受限，建议复制到: {suggested_path}")
                                else:
                                    state["file_data"] = {"error": error_msg}
                                    print(f"   ⚠️ 文件读取失败: {error_msg}")
                        else:
                            state["file_data"] = {"error": "未找到有效的文件路径"}
                            print(f"   ⚠️ 未找到有效的文件路径")

                    except Exception as e:
                        print(f"   ⚠️ 文件系统操作失败: {e}")
                        state["file_data"] = {"error": str(e)}

                elif tool_name == "fetch":
                    # 执行在线搜索 - 简化版本
                    try:
                        query = state.get("user_query", "")
                        print(f"   🔍 正在搜索: {query}")

                        # 生成搜索URL
                        search_url = self._generate_search_url(query)

                        print(f"   🌐 访问URL: {search_url}")
                        result = self.mcp_tools.call_mcp_tool("fetch", "imageFetch", {"url": search_url})

                        state["online_data"] = {
                            "content": result,
                            "source": search_url,
                            "query": query
                        }
                        print(f"   ✅ 在线搜索完成: {len(str(result))} 字符")
                    except Exception as e:
                        print(f"   ⚠️ 在线搜索失败: {e}")
                        # 降级到本地搜索
                        state["online_data"] = {
                            "error": str(e),
                            "fallback": f"无法获取在线信息，建议查阅相关技术文档关于: {state['user_query']}"
                        }

                print(f"   ✅ {self.available_tools[tool_name]['name']} 执行完成")
                
            except Exception as e:
                print(f"   ❌ {self.available_tools[tool_name]['name']} 执行失败: {e}")
        
        return state
    
    async def _query_database(self, query: str) -> Dict[str, Any]:
        """查询数据库"""
        # 简化的数据库查询逻辑
        components = ["叶片", "齿轮箱", "发电机", "变桨", "偏航"]
        for component in components:
            if component in query:
                return await self.db_client.get_faults_by_component(component)
        
        return {"message": "未找到相关数据"}
    
    async def _process_with_ai_node(self, state: InteractiveAgentState) -> InteractiveAgentState:
        """AI处理节点 - Week 4 记忆增强版"""
        print("🤖 使用AI进行综合分析...")

        # 构建上下文
        context_parts = [f"用户问题: {state['user_query']}"]

        # 🚀 新增：处理会话上下文
        conversation_context = state.get("conversation_context", {})
        if conversation_context and conversation_context.get("recent_messages"):
            print(f"   💬 集成会话上下文，历史消息数: {len(conversation_context['recent_messages'])}")

            # 添加上下文摘要
            if conversation_context.get("context_summary"):
                context_parts.append(f"对话背景: {conversation_context['context_summary']}")

            # 添加最近的对话历史
            recent_messages = conversation_context["recent_messages"][-6:]  # 最近3轮对话
            if recent_messages:
                history_context = "最近对话历史:\n"
                for i, msg in enumerate(recent_messages, 1):
                    msg_type = "用户" if msg["type"] == "user" else "AI"
                    content = msg["content"][:100] + "..." if len(msg["content"]) > 100 else msg["content"]
                    history_context += f"{i}. {msg_type}: {content}\n"
                context_parts.append(history_context)

            # 添加关键实体信息
            if conversation_context.get("key_entities"):
                entities = conversation_context["key_entities"]
                entity_info = []
                if entities.get("domain"):
                    entity_info.append(f"讨论领域: {entities['domain']}")
                if entities.get("data_request"):
                    entity_info.append("用户需要数据分析")
                if entities.get("time_reference"):
                    entity_info.append("涉及时间相关查询")

                if entity_info:
                    context_parts.append(f"对话特征: {', '.join(entity_info)}")

        # Phase 6 Week 4: 集成相关记忆到AI分析上下文
        analysis = state.get("query_analysis", {})
        relevant_memories = analysis.get("relevant_memories", [])

        if relevant_memories:
            print(f"   📚 集成 {len(relevant_memories)} 条相关历史经验")
            memory_context = "相关历史经验:\n"
            for i, memory in enumerate(relevant_memories, 1):
                # Week 4 优化: 减少记忆内容长度，避免API超时
                memory_summary = f"{i}. 问题: {memory.user_query[:50]}...\n"
                memory_summary += f"   解决方案: {memory.ai_response[:100]}...\n"
                memory_summary += f"   质量评分: {memory.quality_score:.2f}\n"
                memory_context += memory_summary
            context_parts.append(memory_context)

        if state.get("file_data"):
            context_parts.append(f"文件内容: {state['file_data']}")

        if state.get("local_data"):
            context_parts.append(f"数据库信息: {state['local_data']}")

        if state.get("tech_docs"):
            context_parts.append(f"技术文档: {state['tech_docs']}")

        if state.get("online_data"):
            context_parts.append(f"在线信息: {state['online_data']}")

        context = "\n\n".join(context_parts)
        
        # 构建增强的系统提示 - Week 4 记忆增强版 + 会话上下文
        system_prompt = "你是专业的风机技术专家，请基于提供的信息给出专业回答。"

        # 🚀 新增：会话上下文处理指导
        if conversation_context and conversation_context.get("recent_messages"):
            system_prompt += f"""

会话上下文提示：您正在与用户进行连续对话，已有 {len(conversation_context['recent_messages'])} 条历史消息。请：
1. 理解对话的连续性，当前问题可能与之前的讨论相关
2. 适当引用之前提到的信息，避免重复解释已知概念
3. 如果用户使用"这个"、"那个"等指代词，结合上下文理解其含义
4. 保持对话的自然流畅，就像与熟悉的同事交流
5. 如果发现与之前回答有矛盾，请主动澄清或更正
6. 根据用户的历史偏好调整回答风格和详细程度"""

        # Phase 6 Week 4: 如果有相关记忆，增强系统提示
        if relevant_memories:
            system_prompt += f"""

重要提示：我为您提供了 {len(relevant_memories)} 条相关的历史经验和解决方案。请：
1. 参考这些历史经验，但不要完全照搬
2. 结合当前问题的具体情况，提供个性化的解决方案
3. 如果历史经验中有成功的方法，可以优先推荐
4. 指出当前问题与历史问题的异同点
5. 基于历史经验提供更深入的技术洞察"""

        if state.get("file_data") and isinstance(state["file_data"], dict):
            if state["file_data"].get("error") == "文件访问权限受限":
                system_prompt += """

特别注意：当遇到文件访问权限问题时，请：
1. 明确说明这是系统安全限制，不是错误
2. 提供清晰的解决步骤（复制文件到指定目录）
3. 解释为什么有这个限制（安全考虑）
4. 给出具体的操作命令或步骤
5. 保持专业和友好的语调"""

        # 使用多API客户端调用
        messages = self.ai_client.format_messages(system_prompt, context)

        # Week 4 修复: 传递正确的超时配置，特别是包含历史记忆的复杂查询
        timeout = 120 if relevant_memories else 60  # 有记忆时使用更长超时

        # Phase 7 Week 2: 流式AI回答支持
        stream_enabled = state.get("stream_mode", False)
        if stream_enabled:
            print("🌊 启用流式AI回答模式，您将看到实时回答过程...")
            print("\n" + "="*80)
            print("📝 AI正在实时回答:")
            print("="*80)
            # 移植测试脚本的成功经验：减少干扰输出

        ai_result = await self.ai_client.chat_completion_with_fallback(
            messages,
            original_query=state["user_query"],
            max_tokens=2000,
            temperature=0.7,
            timeout=timeout,
            stream=stream_enabled
        )

        if ai_result["success"]:
            # 🚀 范式转移：使用内容结构化解析器处理AI回答
            raw_ai_content = ai_result["content"]

            if CONTENT_PARSER_AVAILABLE:
                try:
                    print("   🔄 使用结构化解析器处理AI回答...")
                    # 🚀 Phase 1: 传递工具结果用于来源标注
                    tool_results = self._extract_tool_results_for_attribution(state)
                    parser = ContentStructureParser(tool_results=tool_results)
                    structured_content = parser.parse_markdown_to_structure(raw_ai_content)

                    # 设置结构化响应格式
                    state["final_response"] = {
                        "type": "structured_response",
                        "content": structured_content,
                        "raw_markdown": raw_ai_content,  # 保留原始内容用于向后兼容
                        "timestamp": time.time()
                    }
                    print(f"   ✅ 结构化解析完成，生成 {len(structured_content)} 个内容元素")

                except Exception as e:
                    print(f"   ⚠️ 结构化解析失败，使用原始格式: {e}")
                    state["final_response"] = raw_ai_content
            else:
                # 降级处理：使用原始Markdown格式
                state["final_response"] = raw_ai_content
                print("   ⚠️ 内容解析器不可用，使用原始Markdown格式")

            # 显示API使用信息
            api_used = ai_result.get("api_used", "unknown")
            execution_time = ai_result.get("execution_time", 0)

            if api_used == "qwen":
                print(f"   ✅ AI分析完成 (百炼API, {execution_time:.2f}秒)")
            elif api_used == "gemini":
                print(f"   ✅ AI分析完成 (Gemini API, {execution_time:.2f}秒)")
            elif api_used == "modelscope":
                print(f"   ✅ AI分析完成 (ModelScope API, {execution_time:.2f}秒)")
            elif api_used == "local_fallback":
                print(f"   ✅ AI分析完成 (本地回答, {execution_time:.2f}秒)")
                if "warning" in ai_result:
                    print(f"   ⚠️  {ai_result['warning']}")
            else:
                print(f"   ✅ AI分析完成 ({api_used}, {execution_time:.2f}秒)")

            # Phase 6 Week 4: 智能记忆存储
            if self.memory_manager and state.get("query_analysis", {}).get("semantic_result"):
                try:
                    semantic_result = state["query_analysis"]["semantic_result"]
                    interaction_context = {
                        "tools_used": state.get("selected_tools", []),
                        "query_type": state["query_analysis"].get("query_type", ""),
                        "execution_time": state.get("execution_time", 0),
                        "api_used": api_used
                    }

                    memory_id = self.memory_manager.store_memory(
                        semantic_result,
                        state["user_query"],
                        state["final_response"],
                        interaction_context
                    )

                    if memory_id:
                        print(f"   💾 智能记忆已存储: {memory_id}")

                except Exception as e:
                    print(f"   ⚠️ 记忆存储失败: {e}")
        else:
            state["final_response"] = f"AI分析失败: {ai_result['error']}"
            print(f"   ❌ AI分析失败: {ai_result['error']}")

        return state
    
    async def _generate_response_node(self, state: InteractiveAgentState) -> InteractiveAgentState:
        """生成响应节点"""
        print("📝 生成最终响应...")
        
        # 添加执行信息
        if state["selected_tools"]:
            tools_used = [self.available_tools[tool]["name"] for tool in state["selected_tools"]]
            footer = f"\n\n---\n💡 本回答基于: {', '.join(tools_used)} + AI分析"
        else:
            footer = f"\n\n---\n💡 本回答基于: AI专业分析"
        
        state["final_response"] += footer
        
        return state
    
    async def process_query(self, user_query: str) -> Dict[str, Any]:
        """处理用户查询"""
        start_time = time.time()
        
        # 初始化状态
        initial_state = {
            "user_query": user_query,
            "messages": [HumanMessage(content=user_query)],
            "query_analysis": {},
            "available_tools": [],
            "selected_tools": [],
            "local_data": {},
            "online_data": {},
            "tech_docs": {},
            "file_data": {},
            "user_choice": None,
            "needs_confirmation": False,
            "interaction_history": [],
            "final_response": "",
            "execution_time": 0.0,
            "error": None
        }
        
        try:
            # 执行状态图
            result = await self.graph.ainvoke(initial_state)
            
            execution_time = time.time() - start_time
            
            return {
                "success": True,
                "final_response": result["final_response"],
                "execution_time": execution_time,
                "tools_used": result["selected_tools"],
                "query_analysis": result["query_analysis"],
                "stream_mode": result.get("stream_mode", False)
            }
            
        except Exception as e:
            logger.error(f"处理查询失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "final_response": "抱歉，处理您的请求时发生了错误。"
            }

    def get_semantic_performance_report(self) -> Dict[str, Any]:
        """获取语义分析性能报告"""
        if self.semantic_router:
            return self.semantic_router.get_performance_report()
        else:
            return {"message": "混合语义分析系统未启用"}

    def print_semantic_performance_summary(self):
        """打印语义分析性能摘要"""
        if not self.semantic_router:
            return

        report = self.semantic_router.get_performance_report()

        if report.get("total_queries", 0) > 0:
            print(f"\n{'='*60}")
            print("📊 混合语义分析系统性能报告")
            print("-" * 60)
            print(f"总查询数: {report['total_queries']}")
            print(f"基础分析: {report['basic_analysis_ratio']:.1%}")
            print(f"高级分析: {report['advanced_analysis_ratio']:.1%}")
            print(f"平均处理时间: {report['average_processing_time']:.4f}秒")
            print(f"当前性能模式: {report['current_mode']}")

            # Phase 6 Week 4: 添加记忆统计
            if self.memory_manager:
                memory_stats = self.memory_manager.get_memory_statistics()
                print("-" * 60)
                print("🧠 智能记忆系统统计")
                print(f"存储记忆数: {memory_stats['total_memories']}")
                if memory_stats['total_memories'] > 0:
                    print(f"平均质量: {memory_stats['average_quality']:.2f}")
                    print(f"最近7天: {memory_stats['recent_memories']}条")
                    if memory_stats.get('domain_distribution'):
                        top_domain = max(memory_stats['domain_distribution'].items(), key=lambda x: x[1])
                        print(f"主要领域: {top_domain[0]} ({top_domain[1]}次)")

    def _extract_tool_results_for_attribution(self, state: InteractiveAgentState) -> Dict[str, Any]:
        """提取工具结果用于来源标注

        Args:
            state: 智能体状态

        Returns:
            工具结果字典，用于来源标注
        """
        tool_results = {}

        # 提取各种工具的数据
        if state.get("local_data"):
            tool_results["wind-turbine-db"] = {
                "success": True,
                "data": state["local_data"],
                "source": "风机数据库"
            }

        if state.get("tech_docs"):
            # 技术文档可能来自多个工具
            tool_results["local-knowledge-base"] = {
                "success": True,
                "data": state["tech_docs"],
                "source": "本地知识库"
            }
            tool_results["context7"] = {
                "success": True,
                "data": state["tech_docs"],
                "source": "技术文档库"
            }

        if state.get("online_data"):
            tool_results["fetch"] = {
                "success": True,
                "data": state["online_data"],
                "source": "在线信息"
            }

        if state.get("file_data"):
            tool_results["pdf-processor"] = {
                "success": True,
                "data": state["file_data"],
                "source": "PDF文档"
            }
            tool_results["filesystem"] = {
                "success": True,
                "data": state["file_data"],
                "source": "文件系统"
            }

        return tool_results
