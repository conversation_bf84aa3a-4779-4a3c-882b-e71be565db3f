"""
内容结构化解析器
将AI生成的Markdown内容解析为结构化JSON数据，支持表格、数学公式、段落等多种内容类型
实现"范式转移"：将复杂的内容解析逻辑从前端转移到后端
"""

import re
import logging
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class ContentElement:
    """内容元素基类"""
    type: str
    content: Any
    position: int = 0
    source_attribution: Optional[Dict[str, Any]] = None

@dataclass
class SourceAttribution:
    """来源标注数据结构"""
    tool_name: str
    tool_icon: str
    confidence: float
    content_range: Tuple[int, int]
    original_data: Any = None
    source_type: str = "tool"  # tool, ai, mixed

class ContentStructureParser:
    """内容结构化解析器"""

    def __init__(self, tool_results: Optional[Dict[str, Any]] = None):
        """初始化解析器

        Args:
            tool_results: 工具执行结果，用于来源标注
        """
        self.logger = logger
        self.tool_results = tool_results or {}

        # 工具图标映射
        self.tool_icons = {
            "local-knowledge-base": "📚",
            "wind-turbine-db": "🗄️",
            "context7": "📊",
            "fetch": "🌐",
            "pdf-processor": "📄",
            "filesystem": "📁",
            "ai": "🤖"
        }
        
        # 表格匹配正则表达式
        self.table_pattern = re.compile(
            r'^\|.*\|.*$',  # 匹配包含管道符的行
            re.MULTILINE
        )
        
        # 数学公式匹配正则表达式
        self.math_block_pattern = re.compile(r'\$\$(.*?)\$\$', re.DOTALL)
        self.math_inline_pattern = re.compile(r'\$([^$\n]+)\$')
        
        # 标题匹配正则表达式
        self.heading_pattern = re.compile(r'^(#{1,6})\s+(.+)$', re.MULTILINE)
        
        # 列表匹配正则表达式
        self.list_pattern = re.compile(r'^(\s*)([-*+]|\d+\.)\s+(.+)$', re.MULTILINE)
        
        # 代码块匹配正则表达式
        self.code_block_pattern = re.compile(r'```(\w*)\n(.*?)\n```', re.DOTALL)
        self.code_inline_pattern = re.compile(r'`([^`]+)`')

    def parse_markdown_to_structure(self, markdown: str) -> List[Dict[str, Any]]:
        """
        将Markdown文本解析为结构化内容列表

        Args:
            markdown: 原始Markdown文本

        Returns:
            结构化内容元素列表，包含来源标注
        """
        if not markdown or not markdown.strip():
            return []

        self.logger.info(f"🔍 开始解析Markdown内容，长度: {len(markdown)}")
        self.logger.info(f"📊 可用工具结果: {list(self.tool_results.keys())}")

        try:
            # 1. 预处理：标准化换行符和清理格式
            processed_text = self._preprocess_markdown(markdown)

            # 2. 提取所有结构化元素
            elements = []

            # 提取表格
            tables = self._extract_tables(processed_text)
            elements.extend(tables)

            # 提取数学公式并获取清理后的文本
            math_elements, cleaned_text = self._extract_math_formulas(processed_text)
            elements.extend(math_elements)

            # 提取代码块
            code_blocks = self._extract_code_blocks(cleaned_text)
            elements.extend(code_blocks)

            # 提取标题
            headings = self._extract_headings(cleaned_text)
            elements.extend(headings)

            # 提取列表
            lists = self._extract_lists(cleaned_text)
            elements.extend(lists)

            # 3. 按位置排序所有元素
            elements.sort(key=lambda x: x.get('position', 0))

            # 4. 提取剩余的段落内容（使用清理后的文本）
            paragraphs = self._extract_paragraphs(cleaned_text, elements)
            elements.extend(paragraphs)

            # 5. 添加来源标注
            elements_with_sources = self._add_source_attribution(elements, processed_text)

            # 6. 最终排序和清理
            final_elements = self._finalize_elements(elements_with_sources)

            self.logger.info(f"✅ 解析完成，生成 {len(final_elements)} 个结构化元素")
            return final_elements

        except Exception as e:
            self.logger.error(f"❌ Markdown解析失败: {str(e)}")
            # 降级处理：返回单个段落元素
            return [{
                "type": "paragraph",
                "content": markdown,
                "position": 0,
                "source_attribution": self._create_default_source_attribution()
            }]

    def _preprocess_markdown(self, text: str) -> str:
        """预处理Markdown文本"""
        # 标准化换行符
        text = text.replace('\r\n', '\n').replace('\r', '\n')
        
        # 清理多余的空行
        text = re.sub(r'\n{3,}', '\n\n', text)
        
        # 修复常见的表格格式问题
        text = self._fix_table_formatting(text)
        
        return text.strip()

    def _fix_table_formatting(self, text: str) -> str:
        """修复表格格式问题"""
        lines = text.split('\n')
        fixed_lines = []
        
        for line in lines:
            # 检测单行表格格式并转换为多行
            if '|' in line and line.count('|') >= 4:  # 至少包含3个分隔符
                # 可能是压缩的单行表格，尝试展开
                if not any('|' in prev_line for prev_line in lines[max(0, len(fixed_lines)-2):len(fixed_lines)]):
                    # 前面没有表格行，这可能是压缩表格的开始
                    expanded_table = self._expand_compressed_table(line)
                    if expanded_table:
                        fixed_lines.extend(expanded_table)
                        continue
            
            fixed_lines.append(line)
        
        return '\n'.join(fixed_lines)

    def _expand_compressed_table(self, line: str) -> Optional[List[str]]:
        """展开压缩的单行表格"""
        try:
            # 简单的启发式：如果一行包含多个|且内容丰富，尝试展开
            parts = [part.strip() for part in line.split('|') if part.strip()]
            
            if len(parts) >= 3:  # 至少3列才考虑是表格
                # 创建表头
                header = '| ' + ' | '.join(parts[:3]) + ' |'
                # 创建分隔符
                separator = '|' + '---|' * 3
                # 如果有更多内容，创建数据行
                rows = []
                if len(parts) > 3:
                    # 将剩余内容按3个一组分成行
                    remaining = parts[3:]
                    for i in range(0, len(remaining), 3):
                        row_parts = remaining[i:i+3]
                        if len(row_parts) == 3:
                            rows.append('| ' + ' | '.join(row_parts) + ' |')
                
                result = [header, separator] + rows
                self.logger.info(f"🔧 展开压缩表格: {len(result)} 行")
                return result
                
        except Exception as e:
            self.logger.warning(f"表格展开失败: {e}")
            
        return None

    def _extract_tables(self, text: str) -> List[Dict[str, Any]]:
        """提取表格元素"""
        tables = []
        lines = text.split('\n')
        
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            
            # 检测表格开始（包含管道符的行）
            if '|' in line and line.startswith('|') and line.endswith('|'):
                table_lines = []
                start_pos = text.find(lines[i])
                
                # 收集连续的表格行
                while i < len(lines) and '|' in lines[i]:
                    table_lines.append(lines[i].strip())
                    i += 1
                
                # 验证是否为有效表格（至少需要表头和分隔符）
                if len(table_lines) >= 2:
                    table_data = self._parse_table_data(table_lines)
                    if table_data:
                        tables.append({
                            "type": "table",
                            "headers": table_data["headers"],
                            "rows": table_data["rows"],
                            "position": start_pos
                        })
                        self.logger.info(f"📊 提取表格: {len(table_data['headers'])} 列, {len(table_data['rows'])} 行")
                continue
            
            i += 1
        
        return tables

    def _parse_table_data(self, table_lines: List[str]) -> Optional[Dict[str, Any]]:
        """解析表格数据 - 基于Vue Good Table最佳实践优化"""
        try:
            self.logger.info(f"🔍 开始解析表格，总行数: {len(table_lines)}")
            for i, line in enumerate(table_lines):
                self.logger.info(f"  行 {i}: {repr(line)}")

            if len(table_lines) < 3:  # 至少需要表头、分隔符、数据行
                self.logger.warning(f"表格行数不足: {len(table_lines)}, 至少需要3行")
                return None

            # 清理和过滤空行
            clean_lines = []
            for line in table_lines:
                line = line.strip()
                if line and '|' in line:
                    clean_lines.append(line)

            if len(clean_lines) < 3:
                self.logger.warning(f"清理后表格行数不足: {len(clean_lines)}")
                return None

            self.logger.info(f"📋 清理后的表格行数: {len(clean_lines)}")

            # 解析表头
            header_line = clean_lines[0]
            self.logger.info(f"📋 表头行: {repr(header_line)}")

            # 更健壮的表头解析
            header_parts = header_line.split('|')
            headers = []
            for part in header_parts:
                part = part.strip()
                if part:  # 只添加非空的部分
                    headers.append(part)

            self.logger.info(f"📋 解析后的表头: {headers}")

            if not headers:
                self.logger.warning("表头解析失败，没有找到有效的列")
                return None

            # 检查分隔符行（第二行应该包含 --- 或类似的分隔符）
            separator_line = clean_lines[1]
            self.logger.info(f"🔗 分隔符行: {repr(separator_line)}")

            # 解析数据行（从第三行开始）
            data_lines = clean_lines[2:]
            self.logger.info(f"📊 数据行数: {len(data_lines)}")

            rows = []
            for i, line in enumerate(data_lines):
                self.logger.info(f"  处理数据行 {i}: {repr(line)}")

                # 更健壮的数据行解析
                line_parts = line.split('|')
                cells = []
                for part in line_parts:
                    part = part.strip()
                    if part:  # 只添加非空的部分
                        cells.append(part)

                self.logger.info(f"    解析后的单元格: {cells}")

                # 跳过分隔符行（包含 --- 或类似的分隔符）
                if all(cell.replace('-', '').strip() == '' for cell in cells):
                    self.logger.info(f"    ⏭️ 跳过分隔符行: {cells}")
                    continue

                # 检查列数是否匹配（允许一定的容错）
                if len(cells) >= len(headers):
                    # 如果单元格数量多于表头，截取前面的部分
                    row_data = cells[:len(headers)]
                    rows.append(row_data)
                    self.logger.info(f"    ✅ 添加数据行: {row_data}")
                elif len(cells) > 0:
                    # 如果单元格数量少于表头，用空字符串填充
                    while len(cells) < len(headers):
                        cells.append("")
                    rows.append(cells)
                    self.logger.info(f"    ✅ 添加数据行（已填充）: {cells}")
                else:
                    self.logger.warning(f"    ❌ 跳过空数据行")

            self.logger.info(f"📊 最终解析结果: 表头 {len(headers)} 列, 数据 {len(rows)} 行")

            if headers and len(headers) > 0:
                result = {
                    "headers": headers,
                    "rows": rows
                }
                self.logger.info(f"✅ 表格解析成功: {result}")
                return result
            else:
                self.logger.warning("表格解析失败：没有有效的表头")
                return None

        except Exception as e:
            self.logger.error(f"表格解析异常: {e}", exc_info=True)

        return None

    def _extract_math_formulas(self, text: str) -> Tuple[List[Dict[str, Any]], str]:
        """
        提取数学公式元素并从原始文本中清除LaTeX字符串

        Returns:
            Tuple[List[Dict[str, Any]], str]: (公式元素列表, 清理后的文本)
        """
        formulas = []
        cleaned_text = text

        # 提取块级数学公式
        for match in self.math_block_pattern.finditer(text):
            formulas.append({
                "type": "math",
                "formula": match.group(1).strip(),
                "display": "block",
                "position": match.start()
            })

        # 提取行内数学公式
        for match in self.math_inline_pattern.finditer(text):
            formulas.append({
                "type": "math",
                "formula": match.group(1).strip(),
                "display": "inline",
                "position": match.start()
            })

        # 🔧 关键修复：用空格替换LaTeX字符串，保持位置不变，避免重复渲染
        if formulas:
            # 用空格替换块级数学公式的LaTeX语法，保持文本长度
            def replace_with_spaces(match):
                return ' ' * len(match.group(0))

            cleaned_text = self.math_block_pattern.sub(replace_with_spaces, cleaned_text)
            cleaned_text = self.math_inline_pattern.sub(replace_with_spaces, cleaned_text)

            self.logger.info(f"🧮 提取数学公式: {len(formulas)} 个，已用空格替换LaTeX字符串")
        else:
            self.logger.info(f"🧮 提取数学公式: {len(formulas)} 个")

        return formulas, cleaned_text

    def _extract_code_blocks(self, text: str) -> List[Dict[str, Any]]:
        """提取代码块元素"""
        code_blocks = []
        
        # 提取代码块
        for match in self.code_block_pattern.finditer(text):
            code_blocks.append({
                "type": "code_block",
                "language": match.group(1) or "text",
                "content": match.group(2).strip(),
                "position": match.start()
            })
        
        # 提取行内代码
        for match in self.code_inline_pattern.finditer(text):
            code_blocks.append({
                "type": "code_inline",
                "content": match.group(1),
                "position": match.start()
            })
        
        self.logger.info(f"💻 提取代码元素: {len(code_blocks)} 个")
        return code_blocks

    def _extract_headings(self, text: str) -> List[Dict[str, Any]]:
        """提取标题元素"""
        headings = []
        
        for match in self.heading_pattern.finditer(text):
            level = len(match.group(1))  # 计算#的数量
            content = match.group(2).strip()
            
            headings.append({
                "type": "heading",
                "level": level,
                "content": content,
                "position": match.start()
            })
        
        self.logger.info(f"📝 提取标题: {len(headings)} 个")
        return headings

    def _extract_lists(self, text: str) -> List[Dict[str, Any]]:
        """提取列表元素"""
        lists = []
        lines = text.split('\n')
        
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            
            # 检测列表开始
            list_match = self.list_pattern.match(line)
            if list_match:
                list_items = []
                list_type = "ordered" if list_match.group(2).endswith('.') else "unordered"
                start_pos = text.find(lines[i])
                
                # 收集连续的列表项
                while i < len(lines):
                    current_line = lines[i].strip()
                    current_match = self.list_pattern.match(current_line)
                    
                    if current_match:
                        list_items.append(current_match.group(3))
                        i += 1
                    elif current_line == "":
                        # 空行，继续检查下一行
                        i += 1
                    else:
                        # 非列表行，结束列表收集
                        break
                
                if list_items:
                    lists.append({
                        "type": "list",
                        "list_type": list_type,
                        "items": list_items,
                        "position": start_pos
                    })
                    self.logger.info(f"📋 提取列表: {list_type}, {len(list_items)} 项")
                continue
            
            i += 1
        
        return lists

    def _extract_paragraphs(self, text: str, existing_elements: List[Dict]) -> List[Dict[str, Any]]:
        """提取段落元素（排除已识别的结构化元素）"""
        paragraphs = []
        
        # 创建已占用位置的集合
        occupied_ranges = []
        for element in existing_elements:
            pos = element.get('position', 0)
            # 估算元素长度（简化处理）
            if element['type'] == 'table':
                length = len('|'.join(element['headers'])) * (len(element['rows']) + 2)
            elif element['type'] == 'math':
                length = len(element['formula']) + 4  # 加上$$符号
            else:
                length = 50  # 默认长度
            
            occupied_ranges.append((pos, pos + length))
        
        # 按段落分割文本
        paragraphs_text = re.split(r'\n\s*\n', text)
        current_pos = 0
        
        for para_text in paragraphs_text:
            para_text = para_text.strip()
            if not para_text:
                continue
            
            # 检查这个段落是否与已识别元素重叠
            para_start = text.find(para_text, current_pos)
            para_end = para_start + len(para_text)
            
            # 检查是否与已识别元素重叠
            overlaps = any(
                not (para_end <= start or para_start >= end)
                for start, end in occupied_ranges
            )
            
            if not overlaps and len(para_text) > 10:  # 避免太短的段落
                paragraphs.append({
                    "type": "paragraph",
                    "content": para_text,
                    "position": para_start
                })
            
            current_pos = para_end
        
        self.logger.info(f"📄 提取段落: {len(paragraphs)} 个")
        return paragraphs

    def _finalize_elements(self, elements: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """最终化元素列表"""
        # 按位置排序
        elements.sort(key=lambda x: x.get('position', 0))
        
        # 移除position字段（前端不需要）
        for element in elements:
            element.pop('position', None)
        
        # 过滤空元素 - 修复：支持数学公式、代码等不同类型的元素
        return [elem for elem in elements if (
            elem.get('content') or
            elem.get('headers') or
            elem.get('formula') or  # 数学公式
            elem.get('language') or  # 代码块
            elem.get('text')  # 其他文本内容
        )]

    def _add_source_attribution(self, elements: List[Dict[str, Any]], text: str) -> List[Dict[str, Any]]:
        """为内容元素添加来源标注"""
        self.logger.info("🏷️ 开始添加来源标注...")

        for element in elements:
            # 基于内容类型和工具结果推断来源
            source_attribution = self._infer_source_attribution(element, text)
            element["source_attribution"] = source_attribution

        self.logger.info(f"✅ 来源标注完成，处理 {len(elements)} 个元素")
        return elements

    def _infer_source_attribution(self, element: Dict[str, Any], text: str) -> Dict[str, Any]:
        """推断内容元素的来源标注"""
        element_type = element.get("type", "unknown")
        element_content = str(element.get("content", ""))

        # 1. 基于内容特征推断主要来源
        primary_sources = []

        # 检查是否包含数据库相关内容
        if self._contains_database_content(element_content):
            if "wind-turbine-db" in self.tool_results:
                primary_sources.append({
                    "tool_name": "wind-turbine-db",
                    "tool_icon": self.tool_icons["wind-turbine-db"],
                    "confidence": 0.8,
                    "source_type": "tool"
                })

        # 检查是否包含技术文档内容
        if self._contains_technical_content(element_content):
            for tool in ["local-knowledge-base", "context7"]:
                if tool in self.tool_results:
                    primary_sources.append({
                        "tool_name": tool,
                        "tool_icon": self.tool_icons[tool],
                        "confidence": 0.7,
                        "source_type": "tool"
                    })

        # 检查是否包含在线内容
        if self._contains_online_content(element_content):
            if "fetch" in self.tool_results:
                primary_sources.append({
                    "tool_name": "fetch",
                    "tool_icon": self.tool_icons["fetch"],
                    "confidence": 0.6,
                    "source_type": "tool"
                })

        # 2. 如果没有明确的工具来源，标记为AI生成
        if not primary_sources:
            primary_sources.append({
                "tool_name": "ai",
                "tool_icon": self.tool_icons["ai"],
                "confidence": 0.9,
                "source_type": "ai"
            })

        # 3. 选择置信度最高的来源作为主要来源
        primary_source = max(primary_sources, key=lambda x: x["confidence"])

        return {
            "primary_source": primary_source,
            "all_sources": primary_sources,
            "attribution_method": "content_analysis",
            "element_type": element_type
        }

    def _contains_database_content(self, content: str) -> bool:
        """检查内容是否包含数据库相关信息"""
        database_keywords = [
            "故障记录", "历史数据", "统计", "数据库", "查询结果",
            "故障代码", "报警", "传感器数据", "运行参数"
        ]
        return any(keyword in content for keyword in database_keywords)

    def _contains_technical_content(self, content: str) -> bool:
        """检查内容是否包含技术文档相关信息"""
        technical_keywords = [
            "技术规范", "操作手册", "维护指南", "标准", "规程",
            "技术参数", "设计要求", "安全规定", "检修", "保养"
        ]
        return any(keyword in content for keyword in technical_keywords)

    def _contains_online_content(self, content: str) -> bool:
        """检查内容是否包含在线获取的信息"""
        online_keywords = [
            "最新", "新闻", "报告", "研究", "发布", "更新",
            "网站", "链接", "来源", "参考"
        ]
        return any(keyword in content for keyword in online_keywords)

    def _create_default_source_attribution(self) -> Dict[str, Any]:
        """创建默认的来源标注（用于错误情况）"""
        return {
            "primary_source": {
                "tool_name": "ai",
                "tool_icon": self.tool_icons["ai"],
                "confidence": 0.5,
                "source_type": "ai"
            },
            "all_sources": [{
                "tool_name": "ai",
                "tool_icon": self.tool_icons["ai"],
                "confidence": 0.5,
                "source_type": "ai"
            }],
            "attribution_method": "default",
            "element_type": "unknown"
        }
