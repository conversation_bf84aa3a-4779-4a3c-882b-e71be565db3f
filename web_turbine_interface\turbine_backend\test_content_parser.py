"""
内容结构化解析器测试脚本
验证ContentStructureParser的各种解析功能
"""

import sys
import os
import json
import logging

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from services.content_structure_parser import ContentStructureParser

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_table_parsing():
    """测试表格解析功能"""
    print("\n🧪 测试表格解析功能")
    print("=" * 50)
    
    parser = ContentStructureParser()
    
    # 测试用例1：标准表格
    markdown1 = """
以下是风机主要部件信息：

| 部件名称 | 功能 | 常见故障 |
|---------|------|---------|
| 叶片 | 捕获风能 | 裂纹、变形 |
| 齿轮箱 | 增速传动 | 齿轮磨损、油温过高 |
| 发电机 | 发电 | 绕组故障、轴承磨损 |

这是表格后的段落。
"""
    
    result1 = parser.parse_markdown_to_structure(markdown1)
    print(f"✅ 标准表格测试结果: {len(result1)} 个元素")
    for i, elem in enumerate(result1):
        print(f"  {i+1}. {elem['type']}: {elem.get('content', elem.get('headers', 'N/A'))[:50]}...")
    
    # 测试用例2：压缩表格（单行格式）
    markdown2 = """
风机部件信息：| 叶片 | 捕获风能 | 裂纹变形 | 齿轮箱 | 增速传动 | 齿轮磨损 | 发电机 | 发电 | 绕组故障 |

这是后续内容。
"""
    
    result2 = parser.parse_markdown_to_structure(markdown2)
    print(f"✅ 压缩表格测试结果: {len(result2)} 个元素")
    for i, elem in enumerate(result2):
        print(f"  {i+1}. {elem['type']}: {elem.get('content', elem.get('headers', 'N/A'))[:50]}...")

def test_math_parsing():
    """测试数学公式解析功能"""
    print("\n🧪 测试数学公式解析功能")
    print("=" * 50)
    
    parser = ContentStructureParser()
    
    markdown = """
风机效率计算公式如下：

$$\\eta = \\frac{Q \\times Pt}{1000 \\times P_{input}} \\times 100\\%$$

其中 $\\eta$ 是效率，$Q$ 是流量。

功率计算：$$P = \\frac{1}{2} \\rho A v^3$$
"""
    
    result = parser.parse_markdown_to_structure(markdown)
    print(f"✅ 数学公式测试结果: {len(result)} 个元素")
    for i, elem in enumerate(result):
        if elem['type'] == 'math':
            print(f"  {i+1}. {elem['type']} ({elem['display']}): {elem['formula'][:30]}...")
        else:
            print(f"  {i+1}. {elem['type']}: {elem.get('content', 'N/A')[:50]}...")

def test_mixed_content():
    """测试混合内容解析"""
    print("\n🧪 测试混合内容解析功能")
    print("=" * 50)
    
    parser = ContentStructureParser()
    
    markdown = """
# 风机故障诊断报告

## 1. 基本信息

风机运行状态分析如下：

| 参数 | 数值 | 状态 |
|------|------|------|
| 转速 | 1800 rpm | 正常 |
| 温度 | 65°C | 偏高 |
| 振动 | 2.5 mm/s | 异常 |

## 2. 效率分析

效率计算公式：

$$\\eta = \\frac{P_{out}}{P_{in}} \\times 100\\%$$

其中：
- $P_{out}$ 是输出功率
- $P_{in}$ 是输入功率

## 3. 建议措施

1. 检查轴承润滑情况
2. 监控温度变化趋势
3. 分析振动频谱

代码示例：

```python
def calculate_efficiency(p_out, p_in):
    return (p_out / p_in) * 100
```

以上是完整的诊断报告。
"""
    
    result = parser.parse_markdown_to_structure(markdown)
    print(f"✅ 混合内容测试结果: {len(result)} 个元素")
    for i, elem in enumerate(result):
        if elem['type'] == 'table':
            print(f"  {i+1}. {elem['type']}: {len(elem['headers'])} 列, {len(elem['rows'])} 行")
        elif elem['type'] == 'math':
            print(f"  {i+1}. {elem['type']} ({elem['display']}): {elem['formula'][:30]}...")
        elif elem['type'] == 'heading':
            print(f"  {i+1}. {elem['type']} (H{elem['level']}): {elem['content']}")
        elif elem['type'] == 'list':
            print(f"  {i+1}. {elem['type']} ({elem['list_type']}): {len(elem['items'])} 项")
        elif elem['type'] == 'code_block':
            print(f"  {i+1}. {elem['type']} ({elem['language']}): {len(elem['content'])} 字符")
        else:
            print(f"  {i+1}. {elem['type']}: {elem.get('content', 'N/A')[:50]}...")

def test_json_output():
    """测试JSON输出格式"""
    print("\n🧪 测试JSON输出格式")
    print("=" * 50)
    
    parser = ContentStructureParser()
    
    markdown = """
## 测试表格

| 名称 | 值 |
|------|-----|
| 测试1 | 结果1 |
| 测试2 | 结果2 |

计算公式：$$E = mc^2$$

这是一个段落。
"""
    
    result = parser.parse_markdown_to_structure(markdown)
    
    # 转换为JSON格式
    json_output = {
        "type": "structured_response",
        "content": result,
        "timestamp": "2025-07-29T12:00:00Z"
    }
    
    print("✅ JSON输出格式:")
    print(json.dumps(json_output, ensure_ascii=False, indent=2))

def main():
    """主测试函数"""
    print("🚀 内容结构化解析器测试开始")
    print("=" * 80)
    
    try:
        test_table_parsing()
        test_math_parsing()
        test_mixed_content()
        test_json_output()
        
        print("\n" + "=" * 80)
        print("✅ 所有测试完成！解析器功能正常")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
