<template>
  <div class="tool-recommendation-container">
    <!-- 查询分析结果 -->
    <div class="query-analysis" v-if="queryAnalysis">
      <el-tag :type="getQueryTypeColor(queryAnalysis.query_type)" size="small">
        {{ queryAnalysis.query_type }}
      </el-tag>
      <span class="analysis-confidence">
        整体置信度: {{ Math.round(queryAnalysis.confidence * 100) }}%
      </span>
    </div>

    <!-- 工具推荐列表 -->
    <div class="tools-grid">
      <div
        v-for="tool in displayTools"
        :key="tool.name"
        class="tool-card"
        :class="{
          'required': tool.category === 'required',
          'optional': tool.category === 'optional',
          'available': tool.category === 'available',
          'selected': selectedTools.includes(tool.name)
        }"
        @click="toggleTool(tool.name)"
      >
        <!-- 工具头部 -->
        <div class="tool-header">
          <div class="tool-name">
            <span class="tool-icon">{{ getToolIcon(tool.name) }}</span>
            <span class="tool-title">{{ tool.description }}</span>
          </div>
          <div class="tool-category">
            <el-tag
              :type="getToolCategoryType(tool.category)"
              size="mini"
            >
              {{ getToolCategoryLabel(tool.category) }}
            </el-tag>
          </div>
        </div>

        <!-- 工具详情 -->
        <div class="tool-details">
          <div class="detail-row">
            <span class="detail-label">📊 置信度:</span>
            <el-progress 
              :percentage="Math.round(tool.confidence * 100)"
              :color="getConfidenceColor(tool.confidence)"
              :stroke-width="6"
              :show-text="true"
              text-inside
            />
          </div>
          
          <div class="detail-row">
            <span class="detail-label">⏱️ 预计时间:</span>
            <span class="detail-value">{{ tool.estimated_time }}秒</span>
          </div>
          
          <div class="detail-row reason-row">
            <span class="detail-label">💡 推荐理由:</span>
            <span class="detail-value reason-text">{{ tool.reason }}</span>
          </div>
        </div>

        <!-- 选择状态 -->
        <div class="tool-selection" v-if="selectedTools.includes(tool.name)">
          <el-icon class="selection-icon"><Check /></el-icon>
          <span>已选择</span>
        </div>
      </div>
    </div>

    <!-- 选择模式按钮组 -->
    <div class="selection-modes">
      <el-button-group>
        <el-button 
          type="primary" 
          @click="selectMode('required')"
          :disabled="!hasRequiredTools"
        >
          🎯 使用必需工具 ({{ requiredToolsCount }})
        </el-button>
        
        <el-button
          type="success"
          @click="selectMode('all')"
          :disabled="tools.length === 0"
        >
          🔄 使用全部推荐 ({{ tools.length }})
        </el-button>
        
        <el-button 
          type="info" 
          @click="selectMode('ai-only')"
        >
          🤖 仅AI分析
        </el-button>
        
        <el-button
          type="warning"
          @click="selectMode('manual')"
        >
          📋 手动选择 ({{ allAvailableTools.length }}个工具)
        </el-button>
      </el-button-group>
    </div>

    <!-- 确认按钮 -->
    <div class="confirmation-area">
      <el-button 
        type="primary" 
        size="large"
        @click="confirmSelection"
        :disabled="selectedTools.length === 0 && selectionMode !== 'ai-only'"
      >
        确认选择 {{ getSelectionSummary() }}
      </el-button>
    </div>
  </div>
</template>

<script>
import { Check } from '@element-plus/icons-vue'

export default {
  name: 'ToolRecommendation',
  components: { Check },
  props: {
    tools: {
      type: Array,
      default: () => []
    },
    queryAnalysis: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      selectedTools: [],
      selectionMode: 'manual',
      // 所有可用工具的完整列表
      allAvailableTools: [
        {
          name: 'local-knowledge-base',
          description: '本地知识库搜索',
          confidence: 0.8,
          estimated_time: 1,
          reason: '搜索本地PDF知识库，包含14个风机技术文档',
          category: 'available'
        },
        {
          name: 'wind-turbine-db',
          description: '风机数据库查询',
          confidence: 0.8,
          estimated_time: 3,
          reason: '查询风机故障、组件信息和历史记录',
          category: 'available'
        },
        {
          name: 'context7',
          description: '技术文档搜索',
          confidence: 0.8,
          estimated_time: 5,
          reason: '搜索技术文档、操作手册和最佳实践',
          category: 'available'
        },
        {
          name: 'fetch',
          description: '在线信息获取',
          confidence: 0.7,
          estimated_time: 8,
          reason: '获取最新的在线信息和技术资料',
          category: 'available'
        },
        {
          name: 'pdf-processor',
          description: 'PDF文档处理',
          confidence: 0.7,
          estimated_time: 6,
          reason: '处理和搜索PDF技术手册',
          category: 'available'
        },
        {
          name: 'filesystem',
          description: '文件系统操作',
          confidence: 0.7,
          estimated_time: 2,
          reason: '读取、分析本地文件和日志',
          category: 'available'
        }
      ]
    }
  },
  computed: {
    hasRequiredTools() {
      return this.tools.some(tool => tool.category === 'required')
    },
    requiredToolsCount() {
      return this.tools.filter(tool => tool.category === 'required').length
    },
    // 根据选择模式返回不同的工具列表
    displayTools() {
      if (this.selectionMode === 'manual') {
        // 手动选择模式：显示所有可用工具
        return this.allAvailableTools
      } else {
        // 其他模式：显示智能路由器推荐的工具
        return this.tools
      }
    }
  },
  methods: {
    getToolIcon(toolName) {
      const icons = {
        'local-knowledge-base': '⚙️',
        'wind-turbine-db': '🔧',
        'context7': '📚',
        'fetch': '🌐',
        'pdf-processor': '📄',
        'filesystem': '📁',
        'playwright': '🎭',
        'file-operations': '📁',
        'memory-manager': '🧠'
      }
      return icons[toolName] || '⚙️'
    },
    
    getQueryTypeColor(queryType) {
      const colors = {
        '故障诊断': 'danger',
        '操作指导': 'warning',
        '技术查询': 'primary',
        '文档搜索': 'info',
        '在线搜索': 'success'
      }
      return colors[queryType] || 'info'
    },
    
    getConfidenceColor(confidence) {
      if (confidence >= 0.8) return '#67c23a'
      if (confidence >= 0.6) return '#e6a23c'
      return '#f56c6c'
    },

    getToolCategoryType(category) {
      const types = {
        'required': 'danger',
        'optional': 'info',
        'available': 'success'
      }
      return types[category] || 'info'
    },

    getToolCategoryLabel(category) {
      const labels = {
        'required': '必需',
        'optional': '可选',
        'available': '可用'
      }
      return labels[category] || '未知'
    },
    
    toggleTool(toolName) {
      if (this.selectionMode !== 'manual') return
      
      const index = this.selectedTools.indexOf(toolName)
      if (index > -1) {
        this.selectedTools.splice(index, 1)
      } else {
        this.selectedTools.push(toolName)
      }
    },
    
    selectMode(mode) {
      this.selectionMode = mode
      
      switch (mode) {
        case 'required':
          this.selectedTools = this.tools
            .filter(tool => tool.category === 'required')
            .map(tool => tool.name)
          break
        case 'all':
          this.selectedTools = this.tools.map(tool => tool.name)
          break
        case 'ai-only':
          this.selectedTools = []
          break
        case 'manual':
          // 保持当前选择
          break
      }
    },
    
    getSelectionSummary() {
      if (this.selectionMode === 'ai-only') return '(仅AI分析)'
      if (this.selectedTools.length === 0) return ''
      if (this.selectedTools.length === 1) return `(${this.selectedTools[0]})`
      return `(${this.selectedTools.length}个工具)`
    },
    
    confirmSelection() {
      this.$emit('tool-selected', {
        tools: this.selectedTools,
        mode: this.selectionMode,
        analysis: this.queryAnalysis
      })
    }
  }
}
</script>

<style scoped>
.tool-recommendation-container {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 16px 0;
}

.query-analysis {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.analysis-confidence {
  font-weight: 500;
  color: #606266;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.tool-card {
  background: white;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.tool-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px rgba(64, 158, 255, 0.1);
}

.tool-card.required {
  border-color: #f56c6c;
  background: linear-gradient(135deg, #fff 0%, #fef0f0 100%);
}

.tool-card.optional {
  border-color: #909399;
  background: linear-gradient(135deg, #fff 0%, #f4f4f5 100%);
}

.tool-card.available {
  border-color: #67c23a;
  background: linear-gradient(135deg, #fff 0%, #f0f9ff 100%);
}

.tool-card.selected {
  border-color: #67c23a;
  background: linear-gradient(135deg, #fff 0%, #f0f9ff 100%);
}

.tool-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.tool-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tool-icon {
  font-size: 20px;
}

.tool-title {
  font-weight: 600;
  color: #303133;
}

.tool-details {
  space-y: 8px;
}

.detail-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.detail-label {
  font-size: 12px;
  color: #909399;
  min-width: 80px;
}

.detail-value {
  font-weight: 500;
  color: #606266;
}

.reason-row {
  align-items: flex-start;
}

.reason-text {
  font-size: 12px;
  line-height: 1.4;
  color: #909399;
}

.tool-selection {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  align-items: center;
  gap: 4px;
  color: #67c23a;
  font-size: 12px;
  font-weight: 500;
}

.selection-icon {
  font-size: 16px;
}

.selection-modes {
  margin-bottom: 16px;
  text-align: center;
}

.confirmation-area {
  text-align: center;
}
</style>
