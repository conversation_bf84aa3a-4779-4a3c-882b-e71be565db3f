# 🚀 新窗口任务：C2工具生态系统增强

> **🎯 新窗口快速上手指南**
> 1. 先阅读 `context_engineering/CONTEXT_QUICK.md` 了解项目整体状况
> 2. 查看 `context_engineering/C2-001_前端Markdown渲染修复_完成总结.md` 了解前置任务成果
> 3. 检查 `context_engineering/AD_HOC_TASKS.md` 了解当前问题状态和修复进度
> 4. **重要**: 必须先解决AD_HOC_TASKS.md中的高优先级问题再进行后续开发

## 🏗️ 项目背景与上下文

### 项目概述
**项目名称**: 风机智能体Web系统
**核心功能**: 基于LangGraph的智能风机诊断和技术支持平台
**技术架构**: Vue.js 3 + Element Plus (前端) + FastAPI + WebSocket + LangGraph (后端)
**开发环境**: Windows + viz虚拟环境

### 系统当前状态
- **前端服务**: http://localhost:3000 (Vue.js 3 + Element Plus)
- **后端服务**: http://localhost:8000 (FastAPI + WebSocket + LangGraph)
- **测试框架**: Playwright MCP (生产环境验证)
- **系统稳定性**: A级，6工具生态系统完整运行

### 前置任务成果
- ✅ **C0**: 智能工具推荐增强 - 智能路由器集成，85%-95%查询分类准确率
- ✅ **C1**: 工具生态系统完善 - 6工具系统完整实现和测试
- ✅ **C2-001**: 前端Markdown渲染修复 - 85%完成，代码块和数学公式完美渲染

## 📋 任务概述

**任务代号**: C2工具生态系统增强
**任务类型**: 用户界面和功能完整性优化
**预计时间**: 4-5小时
**难度等级**: ⭐⭐⭐⭐
**优先级**: 高

## 🔥 前置条件：解决AD_HOC_TASKS.md中的紧急问题

### ⚠️ 必须优先解决的问题
在开始C2任务前，**必须**先解决以下高优先级问题：

1. **WebSocket ASGI错误** - 🆕 严重问题
   - 错误: `Unexpected ASGI message 'websocket.send', after sending 'websocket.close'`
   - 影响: 系统稳定性，可能导致后端崩溃
   - 优先级: 最高

2. **表格格式显示异常** - ⚠️ 部分修复
   - 状态: 独立表格正常，混合内容表格需优化
   - 影响: 技术报告专业性
   - 优先级: 中高

## 🎯 任务拆分

### C2-001: 前端Markdown渲染修复 - ✅ 已完成 (85%)
**状态**: 基本完成，剩余边缘情况优化
- ✅ 代码块渲染完美修复
- ✅ 数学公式渲染完美修复  
- ⚠️ 表格渲染部分修复（独立表格正常）
- ✅ 架构升级到marked.js + KaTeX

### C2-002: 手动工具选择功能实现 (120分钟)
**目标**: 实现完整的手动工具选择功能，提升用户控制能力

**具体任务**:
1. 设计手动工具选择界面（复选框模式）
2. 实现6个工具的任意组合选择逻辑
3. 添加工具详细描述和使用场景展示
4. 实现实时预览（预计执行时间、资源消耗）
5. 支持用户偏好的工具组合保存和管理

**验收标准**:
- [ ] 用户可以通过复选框选择任意工具组合
- [ ] 显示每个工具的详细描述和适用场景
- [ ] 提供实时预览功能
- [ ] 支持工具组合的保存和命名

### C2-003: AI回答工具来源标注功能 (90分钟)
**目标**: 为AI回答添加明确的工具来源标注，提升信息可信度

**具体任务**:
1. 设计工具来源标注UI组件
2. 修改AI回答生成逻辑，添加来源信息
3. 实现点击来源标签查看原始数据详情
4. 在回答末尾提供完整的工具使用摘要
5. 支持不同工具来源的差异化标识

**验收标准**:
- [ ] AI回答中每个信息点都有明确的工具来源标注
- [ ] 格式如：`[来源: 本地知识库] 风机叶片损伤检测方法包括...`
- [ ] 支持点击来源标签查看详情
- [ ] 回答末尾有完整的工具使用摘要

### C2-004: WebSocket稳定性修复 (90分钟)
**目标**: 修复WebSocket ASGI错误，确保系统稳定运行

**具体任务**:
1. 分析WebSocket连接状态管理问题
2. 修复异步消息发送时的连接状态检查
3. 解决连接关闭和消息发送之间的竞态条件
4. 优化WebSocket重连机制
5. 添加连接状态监控和错误处理

**验收标准**:
- [ ] 消除ASGI错误日志
- [ ] WebSocket连接稳定，无频繁重连
- [ ] 系统长时间运行无崩溃
- [ ] 错误处理机制完善

### C2-005: 表格渲染优化 (60分钟)
**目标**: 完善混合内容中的表格渲染，实现100%表格显示正确

**具体任务**:
1. 优化表格检测和分离逻辑
2. 改进marked.js对混合内容表格的识别
3. 测试各种表格格式的正确渲染
4. 确保表格与其他Markdown元素的兼容性

**验收标准**:
- [ ] 混合内容中的表格正确渲染为HTML表格
- [ ] 各种表格格式都能正确显示
- [ ] 表格与其他Markdown元素无冲突

### C2-006: 生产环境全面测试 (60分钟)
**目标**: 使用Playwright MCP对完整的C2增强功能进行测试

**具体任务**:
1. 测试手动工具选择功能的完整流程
2. 验证AI回答工具来源标注的准确性
3. 测试WebSocket连接的稳定性
4. 验证表格渲染的完整性
5. 确认整体用户体验的提升

**验收标准**:
- [ ] 所有新功能都能正常工作
- [ ] 系统稳定性测试通过
- [ ] 用户体验显著提升
- [ ] 生产环境长时间稳定运行

## 🛠️ 技术要求

### 开发环境
- **前端服务**: http://localhost:3000 (Vue.js 3 + Element Plus)
- **后端服务**: http://localhost:8000 (FastAPI + WebSocket + LangGraph)
- **测试框架**: Playwright MCP
- **虚拟环境**: viz

### 开发原则
1. **TDD驱动**: 每个功能都要有对应的测试
2. **生产验证**: 使用Playwright MCP进行最终验证
3. **用户体验**: 确保界面响应流畅，信息显示清晰
4. **系统稳定**: 优先解决影响稳定性的问题

### 关键文件
- `web_turbine_interface/turbine_frontend/src/App.vue` - 主要前端逻辑
- `web_turbine_interface/turbine_backend/app/main.py` - WebSocket处理
- `web_turbine_interface/turbine_backend/app/services/langgraph_websocket_adapter.py` - 工具执行逻辑

## 📈 成功指标

### 功能指标
- [ ] WebSocket ASGI错误: 完全消除 ✅
- [ ] 表格渲染: 100%正确显示 ✅
- [ ] 手动工具选择: 完整功能实现 ✅
- [ ] 工具来源标注: 完整信息追溯 ✅

### 用户体验指标
- [ ] 界面专业性: 显著提升 ✅
- [ ] 操作灵活性: 用户控制能力增强 ✅
- [ ] 信息可信度: 来源标注清晰 ✅
- [ ] 系统稳定性: 长时间无故障运行 ✅

## 🚨 风险提示

1. **稳定性风险**: WebSocket修复可能影响现有连接逻辑
2. **性能风险**: 新增功能可能影响响应时间
3. **兼容性风险**: UI改动可能影响现有用户体验

## 🎯 下一步行动

### 立即执行步骤
1. **环境确认**: 确保前端(3000)和后端(8000)服务正常运行
2. **问题分析**: 重点分析WebSocket ASGI错误的根本原因
3. **优先级执行**: 按照C2-004 → C2-002 → C2-003 → C2-005 → C2-006的顺序执行

### 开发原则提醒
- **稳定性优先**: 优先解决影响系统稳定性的WebSocket问题
- **用户体验**: 保持C2-001建立的专业Markdown渲染标准
- **功能完整**: 确保新增功能的完整性和可用性
- **生产验证**: 所有功能都要通过Playwright MCP验证

---

**🚨 重要提醒**:
- 所有开发都在viz虚拟环境下进行
- 前端服务: http://localhost:3000，后端服务: http://localhost:8000
- 使用Playwright MCP做最终实验验证
- 优先解决AD_HOC_TASKS.md中的高优先级问题
- 以实际效果为准，不需要生成冗余的测试报告

**📍 C2任务核心价值**:
- 提升系统专业性和用户信任度
- 增强用户控制能力和操作灵活性
- 确保系统长期稳定运行
- 建立完整的信息可追溯机制
