<template>
  <div class="source-attribution">
    <!-- 内联引用标记 -->
    <span 
      v-if="displayMode === 'inline'" 
      class="inline-source-tag"
      :class="sourceClass"
      @click="showDetails"
      @mouseenter="showTooltip"
      @mouseleave="hideTooltip"
      :title="tooltipText"
    >
      {{ sourceIcon }}
    </span>

    <!-- 段落级来源标签 -->
    <div 
      v-else-if="displayMode === 'paragraph'" 
      class="paragraph-source-tag"
      :class="sourceClass"
      @click="showDetails"
    >
      <span class="source-icon">{{ sourceIcon }}</span>
      <span class="source-name">{{ sourceName }}</span>
      <span class="confidence-indicator" :class="confidenceClass">
        {{ confidenceText }}
      </span>
    </div>

    <!-- 悬浮提示详情 -->
    <div 
      v-if="showTooltipDetails" 
      class="source-tooltip"
      :style="tooltipStyle"
    >
      <div class="tooltip-header">
        <span class="tooltip-icon">{{ sourceIcon }}</span>
        <span class="tooltip-title">{{ sourceName }}</span>
      </div>
      <div class="tooltip-content">
        <div class="confidence-info">
          置信度: {{ Math.round(sourceAttribution.primary_source.confidence * 100) }}%
        </div>
        <div class="source-type">
          类型: {{ sourceTypeText }}
        </div>
        <div v-if="hasMultipleSources" class="multiple-sources">
          涉及 {{ sourceAttribution.all_sources.length }} 个数据源
        </div>
      </div>
    </div>

    <!-- 详细信息弹窗 -->
    <div v-if="showDetailsModal" class="source-details-modal" @click.self="hideDetails">
      <div class="modal-content">
        <div class="modal-header">
          <h3>数据来源详情</h3>
          <button class="close-btn" @click="hideDetails">×</button>
        </div>
        <div class="modal-body">
          <div class="primary-source">
            <h4>主要来源</h4>
            <div class="source-item">
              <span class="source-icon">{{ sourceIcon }}</span>
              <span class="source-name">{{ sourceName }}</span>
              <span class="confidence-badge" :class="confidenceClass">
                {{ confidenceText }}
              </span>
            </div>
          </div>
          
          <div v-if="hasMultipleSources" class="all-sources">
            <h4>所有相关来源</h4>
            <div 
              v-for="(source, index) in sourceAttribution.all_sources" 
              :key="index"
              class="source-item"
            >
              <span class="source-icon">{{ getSourceIcon(source.tool_name) }}</span>
              <span class="source-name">{{ getSourceName(source.tool_name) }}</span>
              <span 
                class="confidence-badge" 
                :class="getConfidenceClass(source.confidence)"
              >
                {{ Math.round(source.confidence * 100) }}%
              </span>
            </div>
          </div>

          <div class="attribution-info">
            <div class="info-item">
              <strong>标注方法:</strong> {{ attributionMethodText }}
            </div>
            <div class="info-item">
              <strong>内容类型:</strong> {{ elementTypeText }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'

export default {
  name: 'SourceAttribution',
  props: {
    sourceAttribution: {
      type: Object,
      required: true
    },
    displayMode: {
      type: String,
      default: 'inline', // 'inline', 'paragraph'
      validator: value => ['inline', 'paragraph'].includes(value)
    }
  },
  setup(props) {
    const showTooltipDetails = ref(false)
    const showDetailsModal = ref(false)
    const tooltipStyle = ref({})

    // 计算属性
    const primarySource = computed(() => props.sourceAttribution.primary_source)
    const sourceIcon = computed(() => primarySource.value.tool_icon)
    const sourceName = computed(() => getSourceName(primarySource.value.tool_name))
    const sourceClass = computed(() => `source-${primarySource.value.source_type}`)
    const confidenceClass = computed(() => getConfidenceClass(primarySource.value.confidence))
    const confidenceText = computed(() => `${Math.round(primarySource.value.confidence * 100)}%`)
    const hasMultipleSources = computed(() => props.sourceAttribution.all_sources.length > 1)
    
    const sourceTypeText = computed(() => {
      const typeMap = {
        'tool': '工具数据',
        'ai': 'AI生成',
        'mixed': '混合来源'
      }
      return typeMap[primarySource.value.source_type] || '未知'
    })

    const attributionMethodText = computed(() => {
      const methodMap = {
        'content_analysis': '内容分析',
        'default': '默认标注',
        'manual': '手动标注'
      }
      return methodMap[props.sourceAttribution.attribution_method] || '未知'
    })

    const elementTypeText = computed(() => {
      const typeMap = {
        'paragraph': '段落',
        'table': '表格',
        'math': '数学公式',
        'code': '代码块',
        'heading': '标题',
        'list': '列表'
      }
      return typeMap[props.sourceAttribution.element_type] || '未知'
    })

    const tooltipText = computed(() => {
      return `来源: ${sourceName.value} (${confidenceText.value})`
    })

    // 方法
    const showTooltip = (event) => {
      showTooltipDetails.value = true
      updateTooltipPosition(event)
    }

    const hideTooltip = () => {
      showTooltipDetails.value = false
    }

    const showDetails = () => {
      showDetailsModal.value = true
    }

    const hideDetails = () => {
      showDetailsModal.value = false
    }

    const updateTooltipPosition = (event) => {
      const rect = event.target.getBoundingClientRect()
      tooltipStyle.value = {
        position: 'fixed',
        top: `${rect.bottom + 8}px`,
        left: `${rect.left}px`,
        zIndex: 1000
      }
    }

    const getSourceName = (toolName) => {
      const nameMap = {
        'local-knowledge-base': '本地知识库',
        'wind-turbine-db': '风机数据库',
        'context7': '技术文档库',
        'fetch': '在线信息',
        'pdf-processor': 'PDF文档',
        'filesystem': '文件系统',
        'ai': 'AI分析'
      }
      return nameMap[toolName] || toolName
    }

    const getSourceIcon = (toolName) => {
      const iconMap = {
        'local-knowledge-base': '📚',
        'wind-turbine-db': '🗄️',
        'context7': '📊',
        'fetch': '🌐',
        'pdf-processor': '📄',
        'filesystem': '📁',
        'ai': '🤖'
      }
      return iconMap[toolName] || '❓'
    }

    const getConfidenceClass = (confidence) => {
      if (confidence >= 0.8) return 'confidence-high'
      if (confidence >= 0.6) return 'confidence-medium'
      return 'confidence-low'
    }

    // 生命周期
    onMounted(() => {
      document.addEventListener('scroll', hideTooltip)
    })

    onUnmounted(() => {
      document.removeEventListener('scroll', hideTooltip)
    })

    return {
      showTooltipDetails,
      showDetailsModal,
      tooltipStyle,
      sourceIcon,
      sourceName,
      sourceClass,
      confidenceClass,
      confidenceText,
      hasMultipleSources,
      sourceTypeText,
      attributionMethodText,
      elementTypeText,
      tooltipText,
      showTooltip,
      hideTooltip,
      showDetails,
      hideDetails,
      getSourceName,
      getSourceIcon,
      getConfidenceClass
    }
  }
}
</script>

<style scoped>
/* 内联引用标记样式 */
.inline-source-tag {
  display: inline-block;
  font-size: 0.8em;
  padding: 2px 4px;
  margin: 0 2px;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s ease;
  vertical-align: super;
}

.inline-source-tag:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* 段落级来源标签样式 */
.paragraph-source-tag {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  margin: 8px 0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9em;
}

.paragraph-source-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* 来源类型样式 */
.source-tool {
  background-color: #e3f2fd;
  border: 1px solid #2196f3;
  color: #1976d2;
}

.source-ai {
  background-color: #f3e5f5;
  border: 1px solid #9c27b0;
  color: #7b1fa2;
}

.source-mixed {
  background-color: #fff3e0;
  border: 1px solid #ff9800;
  color: #f57c00;
}

/* 置信度指示器 */
.confidence-indicator, .confidence-badge {
  font-size: 0.8em;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: bold;
}

.confidence-high {
  background-color: #4caf50;
  color: white;
}

.confidence-medium {
  background-color: #ff9800;
  color: white;
}

.confidence-low {
  background-color: #f44336;
  color: white;
}

/* 悬浮提示样式 */
.source-tooltip {
  background: #333;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.85em;
  box-shadow: 0 4px 12px rgba(0,0,0,0.3);
  max-width: 250px;
}

.tooltip-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 6px;
  font-weight: bold;
}

.tooltip-content {
  font-size: 0.9em;
  line-height: 1.4;
}

/* 详细信息弹窗样式 */
.source-details-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.close-btn:hover {
  color: #333;
}

.modal-body {
  padding: 20px;
}

.primary-source, .all-sources {
  margin-bottom: 20px;
}

.primary-source h4, .all-sources h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 1.1em;
}

.source-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 8px;
}

.source-icon {
  font-size: 1.2em;
}

.source-name {
  flex: 1;
  font-weight: 500;
}

.attribution-info {
  border-top: 1px solid #eee;
  padding-top: 16px;
}

.info-item {
  margin-bottom: 8px;
  color: #666;
  font-size: 0.9em;
}
</style>
