# 新窗口任务：SourceAttribution组件修复 🔧

## 📋 任务概述
- **任务编号**: C7-002
- **任务名称**: SourceAttribution组件渲染失效修复
- **优先级**: 🔴 高优先级 (影响专业可信度)
- **预估时间**: 2-3小时
- **前置条件**: C7-001上下文记忆增强测试已完成

## 🎯 任务目标

### 核心问题
通过C7-001测试发现，SourceAttribution组件无法正确渲染，导致AI回答缺乏信息来源标注，严重影响专业技术咨询的可信度和可追溯性。

### 具体表现
- 控制台持续显示：`[WARNING] [Vue warn]: Failed to resolve component: SourceAttribution`
- AI回答中缺少工具来源标注
- 用户无法追溯信息来源，影响专业性

### 成功标准
- ✅ 消除Vue组件解析警告
- ✅ AI回答正确显示信息来源标注
- ✅ 支持点击查看详细来源信息
- ✅ 通过Playwright MCP端到端验证

## 🔍 技术分析

### 问题定位
1. **Vue组件注册问题**: SourceAttribution组件可能未正确注册到Vue应用
2. **导入路径问题**: 组件文件路径或导入语句可能有误
3. **组件定义问题**: 组件本身可能存在语法或结构错误
4. **依赖问题**: 组件依赖的其他模块可能缺失

### 相关文件
- `web_turbine_interface/turbine_frontend/src/components/SourceAttribution.vue` (组件文件)
- `web_turbine_interface/turbine_frontend/src/App.vue` (主应用文件)
- `web_turbine_interface/turbine_frontend/src/main.js` (应用入口)

## 🛠️ 修复步骤

### Step 1: 诊断组件状态 (30分钟)
```bash
# 1. 检查组件文件是否存在
find web_turbine_interface/turbine_frontend -name "*SourceAttribution*" -type f

# 2. 检查组件导入和注册
grep -r "SourceAttribution" web_turbine_interface/turbine_frontend/src/

# 3. 启动开发服务器观察控制台
cd web_turbine_interface/turbine_frontend
npm run dev
```

### Step 2: 修复组件问题 (60-90分钟)
1. **检查组件定义**
   - 验证Vue 3 Composition API语法
   - 确保export default正确
   - 检查template、script、style结构

2. **修复导入注册**
   - 确保组件正确导入到使用它的文件中
   - 检查components选项或全局注册
   - 验证文件路径正确性

3. **修复依赖问题**
   - 检查组件依赖的其他模块
   - 确保所有import语句正确
   - 验证props和emit定义

### Step 3: TDD验证修复效果 (30分钟)
```bash
# 使用Playwright MCP进行端到端测试
# 1. 启动前后端服务
# 前端: http://localhost:3000
# 后端: http://localhost:8000

# 2. 发送测试查询
# 3. 验证AI回答显示来源标注
# 4. 检查控制台无Vue警告
```

## 📊 验证标准

### 功能验证
- [ ] 控制台无SourceAttribution相关Vue警告
- [ ] AI回答显示工具来源标注 (如: [来源: 本地知识库])
- [ ] 来源标注支持交互 (点击查看详情)
- [ ] 不同工具来源有差异化显示

### 技术验证
- [ ] Vue组件正确注册和渲染
- [ ] 组件props正确接收数据
- [ ] 组件样式正确应用
- [ ] 无JavaScript运行时错误

### 端到端验证
- [ ] 发送查询："测试工具来源标注：请介绍风机故障诊断流程"
- [ ] 验证AI回答包含明确的工具来源标注
- [ ] 验证来源标注的交互功能正常
- [ ] 验证多工具查询的来源区分显示

## 🚨 重要注意事项

### 保持现有功能
- **不要破坏**: C0-C7已完成的功能成果
- **保持兼容**: 上下文记忆、智能工具推荐等核心功能
- **维持性能**: 不影响现有的响应时间和用户体验

### 开发原则
1. **TDD优先**: 每个修复都要通过Playwright MCP验证
2. **渐进修复**: 先解决基础渲染问题，再优化交互功能
3. **代码质量**: 遵循Vue 3最佳实践和项目代码规范
4. **文档更新**: 修复完成后更新相关技术文档

### 测试环境
- **前端服务**: http://localhost:3000
- **后端服务**: http://localhost:8000
- **测试工具**: Playwright MCP浏览器自动化
- **验证方式**: 端到端功能测试 + 控制台错误检查

## 📈 预期成果

### 直接成果
- SourceAttribution组件正常渲染
- AI回答恢复信息来源标注功能
- 提升专业技术咨询的可信度

### 技术价值
- 完善Vue组件架构
- 提升信息可追溯性
- 为后续Phase C7开发扫清障碍

### 用户价值
- 增强AI回答的专业可信度
- 支持信息来源验证和追溯
- 提升整体用户体验

## 🔄 后续计划

修复完成后，可以继续Phase C7的其他智能化功能增强任务：
- C7-003: 个性化服务优化
- C7-004: 智能推荐算法升级
- C7-005: 多模态交互支持

---

**🎯 核心目标**: 恢复AI回答的信息来源标注功能，提升专业技术咨询的可信度和可追溯性！

**⚡ 关键提醒**: 使用Playwright MCP进行TDD验证，确保修复效果符合预期！
