"""
会话状态管理器 - 实现真正的对话连续性和上下文管理
"""

import asyncio
import json
import logging
import sys
import os
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from collections import deque
import hashlib

# 添加主项目路径以导入IntelligentMemoryManager
main_project_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..'))
src_path = os.path.join(main_project_path, 'src')
sys.path.insert(0, main_project_path)
sys.path.insert(0, src_path)

# 尝试导入智能记忆管理器
try:
    from memory_enhancement.intelligent_memory_manager import IntelligentMemoryManager
    from memory_enhancement.hybrid_semantic_analyzer import AdaptiveSemanticRouter
    MEMORY_ENHANCEMENT_AVAILABLE = True
except ImportError:
    MEMORY_ENHANCEMENT_AVAILABLE = False
    print("⚠️ 智能记忆管理器未找到，使用基础会话管理")

logger = logging.getLogger(__name__)

@dataclass
class MessageContext:
    """消息上下文数据结构"""
    message_id: str
    session_id: str
    user_query: str
    ai_response: str
    tool_results: Dict[str, Any]
    timestamp: datetime
    message_type: str  # 'user', 'ai', 'system'
    source_attribution: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data

@dataclass
class SessionContext:
    """会话上下文数据结构"""
    session_id: str
    created_at: datetime
    updated_at: datetime
    message_history: List[MessageContext]
    context_summary: str
    key_entities: Dict[str, Any]  # 提取的关键实体
    user_preferences: Dict[str, Any]  # 用户偏好
    conversation_state: str  # 'active', 'idle', 'archived'
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'session_id': self.session_id,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'message_history': [msg.to_dict() for msg in self.message_history],
            'context_summary': self.context_summary,
            'key_entities': self.key_entities,
            'user_preferences': self.user_preferences,
            'conversation_state': self.conversation_state
        }

class SessionManager:
    """会话状态管理器"""
    
    def __init__(self, max_sessions: int = 100, max_messages_per_session: int = 50):
        """初始化会话管理器"""
        self.sessions: Dict[str, SessionContext] = {}
        self.max_sessions = max_sessions
        self.max_messages_per_session = max_messages_per_session
        self.cleanup_interval = timedelta(hours=24)  # 24小时清理一次
        self.session_timeout = timedelta(hours=2)    # 2小时无活动则标记为idle

        # 🚀 集成智能记忆管理器
        if MEMORY_ENHANCEMENT_AVAILABLE:
            self.memory_manager = IntelligentMemoryManager("session_memory.json")
            self.semantic_router = AdaptiveSemanticRouter()
            logger.info("✅ 智能记忆管理器已启用")
        else:
            self.memory_manager = None
            self.semantic_router = None
            logger.warning("⚠️ 智能记忆管理器不可用，使用基础功能")

        # 启动后台清理任务
        asyncio.create_task(self._periodic_cleanup())
        
    async def create_session(self, session_id: str) -> SessionContext:
        """创建新会话"""
        if session_id in self.sessions:
            logger.info(f"会话 {session_id} 已存在，返回现有会话")
            return self.sessions[session_id]
            
        session = SessionContext(
            session_id=session_id,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            message_history=[],
            context_summary="",
            key_entities={},
            user_preferences={},
            conversation_state="active"
        )
        
        self.sessions[session_id] = session
        logger.info(f"✅ 创建新会话: {session_id}")
        
        # 如果会话数量超过限制，清理最旧的会话
        if len(self.sessions) > self.max_sessions:
            await self._cleanup_old_sessions()
            
        return session
    
    async def get_session(self, session_id: str) -> Optional[SessionContext]:
        """获取会话上下文"""
        session = self.sessions.get(session_id)
        if session:
            # 更新最后活动时间
            session.updated_at = datetime.now()
            if session.conversation_state == 'idle':
                session.conversation_state = 'active'
                logger.info(f"会话 {session_id} 从idle状态恢复为active")
        return session
    
    async def add_message(self, session_id: str, user_query: str, ai_response: str, 
                         tool_results: Dict[str, Any], source_attribution: Optional[Dict[str, Any]] = None) -> None:
        """添加消息到会话历史"""
        session = await self.get_session(session_id)
        if not session:
            session = await self.create_session(session_id)
        
        # 创建消息上下文
        message_id = self._generate_message_id(session_id, user_query)
        
        # 添加用户消息
        user_message = MessageContext(
            message_id=f"{message_id}_user",
            session_id=session_id,
            user_query=user_query,
            ai_response="",
            tool_results={},
            timestamp=datetime.now(),
            message_type="user"
        )
        
        # 添加AI回复
        ai_message = MessageContext(
            message_id=f"{message_id}_ai",
            session_id=session_id,
            user_query=user_query,
            ai_response=ai_response,
            tool_results=tool_results,
            timestamp=datetime.now(),
            message_type="ai",
            source_attribution=source_attribution
        )
        
        session.message_history.extend([user_message, ai_message])
        session.updated_at = datetime.now()
        
        # 限制消息历史长度
        if len(session.message_history) > self.max_messages_per_session:
            # 保留最近的消息，删除最旧的
            session.message_history = session.message_history[-self.max_messages_per_session:]
            logger.info(f"会话 {session_id} 消息历史已截断到 {self.max_messages_per_session} 条")
        
        # 🚀 智能上下文分析和记忆存储
        if self.memory_manager and self.semantic_router:
            try:
                # 进行语义分析
                semantic_result = await self._analyze_message_semantics(user_query, ai_response, tool_results)

                # 构建交互上下文
                interaction_context = {
                    "session_id": session_id,
                    "tools_used": list(tool_results.keys()),
                    "response_length": len(ai_response),
                    "has_source_attribution": source_attribution is not None,
                    "timestamp": datetime.now().isoformat()
                }

                # 尝试存储到智能记忆系统
                memory_id = self.memory_manager.store_memory(
                    semantic_result, user_query, ai_response, interaction_context
                )

                if memory_id:
                    logger.info(f"🧠 消息已存储到智能记忆系统: {memory_id}")
                    # 将记忆ID添加到AI消息中
                    ai_message.source_attribution = ai_message.source_attribution or {}
                    ai_message.source_attribution["memory_id"] = memory_id

                # 智能上下文摘要更新
                await self._update_intelligent_context_summary(session, semantic_result)

            except Exception as e:
                logger.error(f"❌ 智能上下文分析失败: {e}")
                # 降级到基础分析
                await self._update_context_summary(session)
                await self._extract_key_entities(session, user_query, ai_response)
        else:
            # 使用基础上下文分析
            await self._update_context_summary(session)
            await self._extract_key_entities(session, user_query, ai_response)

        logger.info(f"✅ 添加消息到会话 {session_id}，当前历史长度: {len(session.message_history)}")
    
    async def get_conversation_context(self, session_id: str, max_messages: int = 10,
                                     current_query: str = None) -> Dict[str, Any]:
        """获取对话上下文，用于AI理解历史对话（智能增强版）"""
        session = await self.get_session(session_id)
        if not session:
            return {
                "session_id": session_id,
                "context_summary": "",
                "recent_messages": [],
                "key_entities": {},
                "user_preferences": {},
                "relevant_memories": [],
                "intelligent_insights": {}
            }

        # 获取最近的消息
        recent_messages = session.message_history[-max_messages:] if session.message_history else []

        # 基础上下文
        context = {
            "session_id": session_id,
            "context_summary": session.context_summary,
            "recent_messages": [
                {
                    "type": msg.message_type,
                    "content": msg.user_query if msg.message_type == "user" else msg.ai_response,
                    "timestamp": msg.timestamp.isoformat(),
                    "tool_results": msg.tool_results if msg.message_type == "ai" else {}
                }
                for msg in recent_messages
            ],
            "key_entities": session.key_entities,
            "user_preferences": session.user_preferences,
            "conversation_state": session.conversation_state,
            "relevant_memories": [],
            "intelligent_insights": {}
        }

        # 🚀 智能上下文增强
        if self.memory_manager and self.semantic_router and current_query:
            try:
                # 分析当前查询
                semantic_result = await self._analyze_message_semantics(current_query, "", {})

                # 检索相关记忆
                relevant_memories = self.memory_manager.retrieve_relevant_memories(semantic_result, limit=3)
                context["relevant_memories"] = [
                    {
                        "memory_id": memory.id,
                        "user_query": memory.user_query[:100] + "..." if len(memory.user_query) > 100 else memory.user_query,
                        "ai_response": memory.ai_response[:200] + "..." if len(memory.ai_response) > 200 else memory.ai_response,
                        "quality_score": memory.quality_score,
                        "timestamp": memory.timestamp,
                        "tags": memory.memory_tags
                    }
                    for memory in relevant_memories
                ]

                # 获取个性化推荐
                recommendations = self.memory_manager.get_personalized_recommendations(semantic_result)
                context["intelligent_insights"] = {
                    "recommended_tools": recommendations.get("suggested_tools", []),
                    "related_domains": recommendations.get("related_domains", []),
                    "complexity_advice": recommendations.get("complexity_advice", ""),
                    "proactive_suggestions": recommendations.get("proactive_suggestions", [])
                }

                logger.info(f"🧠 智能上下文增强完成: {len(relevant_memories)} 条相关记忆")

            except Exception as e:
                logger.error(f"❌ 智能上下文增强失败: {e}")

        return context
    
    async def update_user_preferences(self, session_id: str, preferences: Dict[str, Any]) -> None:
        """更新用户偏好"""
        session = await self.get_session(session_id)
        if session:
            session.user_preferences.update(preferences)
            session.updated_at = datetime.now()
            logger.info(f"✅ 更新会话 {session_id} 用户偏好: {preferences}")
    
    async def archive_session(self, session_id: str) -> bool:
        """归档会话"""
        session = await self.get_session(session_id)
        if session:
            session.conversation_state = "archived"
            session.updated_at = datetime.now()
            logger.info(f"✅ 归档会话: {session_id}")
            return True
        return False
    
    async def delete_session(self, session_id: str) -> bool:
        """删除会话"""
        if session_id in self.sessions:
            del self.sessions[session_id]
            logger.info(f"✅ 删除会话: {session_id}")
            return True
        return False
    
    def get_session_stats(self) -> Dict[str, Any]:
        """获取会话统计信息"""
        active_sessions = sum(1 for s in self.sessions.values() if s.conversation_state == "active")
        idle_sessions = sum(1 for s in self.sessions.values() if s.conversation_state == "idle")
        archived_sessions = sum(1 for s in self.sessions.values() if s.conversation_state == "archived")
        
        return {
            "total_sessions": len(self.sessions),
            "active_sessions": active_sessions,
            "idle_sessions": idle_sessions,
            "archived_sessions": archived_sessions,
            "max_sessions": self.max_sessions
        }
    
    def _generate_message_id(self, session_id: str, user_query: str) -> str:
        """生成消息ID"""
        content = f"{session_id}_{user_query}_{datetime.now().isoformat()}"
        return hashlib.md5(content.encode()).hexdigest()[:12]
    
    async def _update_context_summary(self, session: SessionContext) -> None:
        """更新上下文摘要"""
        if len(session.message_history) < 2:
            return
            
        # 简单的上下文摘要生成（可以后续集成更智能的摘要算法）
        recent_topics = []
        for msg in session.message_history[-6:]:  # 最近3轮对话
            if msg.message_type == "user":
                # 提取用户查询的关键词
                query_words = msg.user_query.lower().split()
                keywords = [word for word in query_words if len(word) > 2][:3]
                recent_topics.extend(keywords)
        
        # 去重并生成摘要
        unique_topics = list(set(recent_topics))
        if unique_topics:
            session.context_summary = f"最近讨论的主题包括: {', '.join(unique_topics[:5])}"
        
    async def _extract_key_entities(self, session: SessionContext, user_query: str, ai_response: str) -> None:
        """提取关键实体"""
        # 简单的实体提取（可以后续集成NER模型）
        entities = session.key_entities
        
        # 提取可能的实体类型
        query_lower = user_query.lower()
        response_lower = ai_response.lower()
        
        # 风机相关实体
        if any(keyword in query_lower for keyword in ['风机', '风力发电', '叶片', '齿轮箱', '发电机']):
            entities['domain'] = 'wind_turbine'
            
        # 数据类型实体
        if any(keyword in query_lower for keyword in ['数据', '报告', '分析', '统计']):
            entities['data_request'] = True
            
        # 时间实体（简单匹配）
        if any(keyword in query_lower for keyword in ['今天', '昨天', '本月', '上月', '最近']):
            entities['time_reference'] = True
    
    async def _cleanup_old_sessions(self) -> None:
        """清理旧会话"""
        current_time = datetime.now()
        sessions_to_remove = []
        
        for session_id, session in self.sessions.items():
            # 标记长时间无活动的会话为idle
            if (current_time - session.updated_at) > self.session_timeout:
                if session.conversation_state == "active":
                    session.conversation_state = "idle"
                    logger.info(f"会话 {session_id} 标记为idle")
            
            # 删除非常旧的archived会话
            if (session.conversation_state == "archived" and 
                (current_time - session.updated_at) > timedelta(days=7)):
                sessions_to_remove.append(session_id)
        
        # 删除标记的会话
        for session_id in sessions_to_remove:
            del self.sessions[session_id]
            logger.info(f"清理旧会话: {session_id}")
        
        # 如果仍然超过限制，删除最旧的idle会话
        if len(self.sessions) > self.max_sessions:
            idle_sessions = [(sid, s) for sid, s in self.sessions.items() if s.conversation_state == "idle"]
            idle_sessions.sort(key=lambda x: x[1].updated_at)
            
            for session_id, _ in idle_sessions[:len(self.sessions) - self.max_sessions]:
                del self.sessions[session_id]
                logger.info(f"清理idle会话: {session_id}")
    
    async def _periodic_cleanup(self) -> None:
        """定期清理任务"""
        while True:
            try:
                await asyncio.sleep(self.cleanup_interval.total_seconds())
                await self._cleanup_old_sessions()
                logger.info(f"定期清理完成，当前会话数: {len(self.sessions)}")
            except Exception as e:
                logger.error(f"定期清理任务出错: {e}")

    # 🚀 智能语义分析辅助方法
    async def _analyze_message_semantics(self, user_query: str, ai_response: str, tool_results: Dict[str, Any]):
        """分析消息的语义信息"""
        if not self.semantic_router:
            return None

        try:
            # 使用语义路由器分析查询
            semantic_result = self.semantic_router.analyze_with_routing(user_query)

            # 增强语义结果（添加工具使用信息）
            if hasattr(semantic_result, 'entities') and semantic_result.entities:
                # 基于工具结果增强实体信息
                if tool_results:
                    for tool_name in tool_results.keys():
                        if tool_name not in semantic_result.entities.get("tools", []):
                            if "tools" not in semantic_result.entities:
                                semantic_result.entities["tools"] = []
                            semantic_result.entities["tools"].append(tool_name)

            return semantic_result

        except Exception as e:
            logger.error(f"❌ 语义分析失败: {e}")
            return None

    async def _update_intelligent_context_summary(self, session: SessionContext, semantic_result):
        """使用智能分析结果更新上下文摘要"""
        if not semantic_result:
            await self._update_context_summary(session)
            return

        try:
            # 基于语义分析结果生成更智能的摘要
            core_concept = getattr(semantic_result, 'core_concept', '未知概念')
            technical_domain = getattr(semantic_result, 'technical_domain', '通用技术')
            intent_depth = getattr(semantic_result, 'intent_depth', 'surface')

            # 构建智能摘要
            summary_parts = []

            if core_concept != '未知概念':
                summary_parts.append(f"核心概念: {core_concept}")

            if technical_domain != '通用技术':
                summary_parts.append(f"技术领域: {technical_domain}")

            if intent_depth != 'surface':
                summary_parts.append(f"分析深度: {intent_depth}")

            # 分析最近的查询模式
            recent_queries = [msg.user_query for msg in session.message_history[-6:] if msg.message_type == "user"]
            if len(recent_queries) >= 2:
                # 简单的模式识别
                if any("故障" in query for query in recent_queries):
                    summary_parts.append("关注点: 故障诊断")
                elif any("数据" in query for query in recent_queries):
                    summary_parts.append("关注点: 数据分析")
                elif any("维护" in query for query in recent_queries):
                    summary_parts.append("关注点: 设备维护")

            session.context_summary = "; ".join(summary_parts) if summary_parts else "智能分析进行中"

            # 更新关键实体（基于语义分析）
            if hasattr(semantic_result, 'entities') and semantic_result.entities:
                session.key_entities.update({
                    "semantic_entities": semantic_result.entities,
                    "core_concept": core_concept,
                    "technical_domain": technical_domain,
                    "intent_depth": intent_depth
                })

            logger.info(f"🧠 智能上下文摘要已更新: {session.context_summary}")

        except Exception as e:
            logger.error(f"❌ 智能上下文摘要更新失败: {e}")
            # 降级到基础摘要
            await self._update_context_summary(session)
