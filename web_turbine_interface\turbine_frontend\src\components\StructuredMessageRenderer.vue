<template>
  <div class="structured-message">
    <!-- 处理结构化响应 -->
    <div v-if="isStructuredResponse" class="structured-content">
      <!-- 渐进式渲染的元素 -->
      <transition-group name="fade-in" tag="div" class="progressive-content">
        <div
          v-for="(element, index) in visibleElements"
          :key="`element-${index}`"
          :class="['progressive-element', { 'animating': index === visibleElements.length - 1 }]"
        >
          <!-- 来源标注（段落级） -->
          <SourceAttribution
            v-if="element.source_attribution && shouldShowSourceAttribution(element)"
            :source-attribution="element.source_attribution"
            display-mode="paragraph"
            class="element-source-attribution"
          />

          <!-- 内容元素 -->
          <component
            :is="getComponentType(element.type)"
            :data="element"
            :index="index"
          />
        </div>
      </transition-group>

      <!-- 正在渲染指示器 -->
      <div v-if="isRendering" class="rendering-indicator">
        <div class="loading-dots">
          <span class="dot"></span>
          <span class="dot"></span>
          <span class="dot"></span>
        </div>
        <span class="rendering-text">正在渲染内容...</span>
      </div>
    </div>

    <!-- 处理传统Markdown响应（向后兼容） -->
    <div v-else class="legacy-content" v-html="formatLegacyMarkdown(messageText)"></div>
  </div>
</template>

<script>
import { ref, computed, watch, onUnmounted, onMounted, onUpdated, nextTick, h } from 'vue'
import SourceAttribution from './SourceAttribution.vue'

export default {
  name: 'StructuredMessageRenderer',
  components: {
    SourceAttribution
  },
  props: {
    messageText: {
      type: [String, Object],
      required: true
    }
  },
  setup(props) {
    // 渐进式渲染状态
    const visibleElements = ref([])
    const isRendering = ref(false)
    const renderingTimer = ref(null)

    // 判断是否为结构化响应
    const isStructuredResponse = computed(() => {
      return typeof props.messageText === 'object' &&
             props.messageText?.type === 'structured_response'
    })

    // 获取结构化内容
    const structuredContent = computed(() => {
      if (isStructuredResponse.value) {
        return props.messageText.content || []
      }
      return []
    })

    // 渐进式渲染函数
    const startProgressiveRendering = () => {
      if (!isStructuredResponse.value || structuredContent.value.length === 0) {
        return
      }

      // 重置状态
      visibleElements.value = []
      isRendering.value = true

      // 清除之前的定时器
      if (renderingTimer.value) {
        clearTimeout(renderingTimer.value)
      }

      // 渐进式显示元素
      const renderNextElement = (index) => {
        if (index >= structuredContent.value.length) {
          // 渲染完成
          isRendering.value = false
          return
        }

        // 添加下一个元素
        visibleElements.value.push(structuredContent.value[index])

        // 根据元素类型设置不同的延迟
        const element = structuredContent.value[index]
        let delay = 300 // 默认延迟

        switch (element.type) {
          case 'paragraph':
            delay = 200 // 段落较快
            break
          case 'table':
            delay = 800 // 表格较慢，需要更多渲染时间
            break
          case 'math':
            delay = 600 // 数学公式需要KaTeX渲染时间
            break
          case 'heading':
            delay = 150 // 标题很快
            break
          case 'list':
            delay = 300 // 列表中等
            break
          case 'code_block':
            delay = 500 // 代码块需要语法高亮时间
            break
          default:
            delay = 300
        }

        // 设置下一个元素的渲染
        renderingTimer.value = setTimeout(() => {
          renderNextElement(index + 1)
        }, delay)
      }

      // 开始渲染第一个元素
      renderNextElement(0)
    }
    
    // 获取组件类型映射
    const getComponentType = (elementType) => {
      const componentMap = {
        'paragraph': 'ParagraphComponent',
        'table': 'TableComponent',
        'math': 'MathComponent',
        'heading': 'HeadingComponent',
        'list': 'ListComponent',
        'code_block': 'CodeBlockComponent',
        'code_inline': 'CodeInlineComponent'
      }
      return componentMap[elementType] || 'ParagraphComponent'
    }
    
    // 处理传统Markdown（简化版，用于向后兼容）
    const formatLegacyMarkdown = (text) => {
      if (typeof text !== 'string') return ''

      // 基础Markdown处理
      return text
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/`(.*?)`/g, '<code>$1</code>')
        .replace(/\n/g, '<br>')
    }

    // 监听结构化内容变化，触发渐进式渲染
    watch(structuredContent, (newContent) => {
      if (newContent && newContent.length > 0) {
        // 延迟一点开始渲染，让DOM有时间准备
        setTimeout(() => {
          startProgressiveRendering()
        }, 100)
      }
    }, { immediate: true })

    // 组件卸载时清理定时器
    onUnmounted(() => {
      if (renderingTimer.value) {
        clearTimeout(renderingTimer.value)
      }
    })

    // 判断是否显示来源标注
    const shouldShowSourceAttribution = (element) => {
      // 检查元素是否有来源标注
      if (!element.source_attribution) {
        return false
      }

      // 检查是否为AI生成且置信度较低的内容（可能不需要显示）
      const primarySource = element.source_attribution.primary_source
      if (primarySource.source_type === 'ai' && primarySource.confidence < 0.7) {
        return false
      }

      // 对于重要的内容类型，总是显示来源标注
      const importantTypes = ['table', 'math', 'code']
      if (importantTypes.includes(element.type)) {
        return true
      }

      // 对于工具来源的内容，总是显示
      if (primarySource.source_type === 'tool') {
        return true
      }

      return true
    }

    return {
      isStructuredResponse,
      structuredContent,
      visibleElements,
      isRendering,
      getComponentType,
      formatLegacyMarkdown,
      startProgressiveRendering,
      shouldShowSourceAttribution
    }
  },
  
  // 注册子组件 - 🚀 范式转移：使用渲染函数替代模板字符串
  components: {
    // 段落组件
    ParagraphComponent: {
      props: ['data'],
      render() {
        const formatParagraph = (content) => {
          if (!content) return ''
          return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
        }

        return h('p', {
          class: 'paragraph-element',
          innerHTML: formatParagraph(this.data.content)
        })
      }
    },

    // 表格组件
    TableComponent: {
      props: ['data'],
      render() {
        // 创建表头
        const headers = this.data.headers || []
        const rows = this.data.rows || []

        const thead = h('thead', [
          h('tr', headers.map(header => h('th', header)))
        ])

        // 创建表体
        const tbody = h('tbody',
          rows.map(row =>
            h('tr', row.map(cell => h('td', cell)))
          )
        )

        const table = h('table', { class: 'structured-table' }, [thead, tbody])

        return h('div', { class: 'table-element' }, [table])
      }
    },
    
    // 数学公式组件 - 🚀 范式转移：使用渲染函数 + Vue 3 Composition API
    MathComponent: {
      props: ['data'],
      setup(props) {
        const mathElement = ref(null)

        const renderMath = () => {
          if (window.katex && mathElement.value) {
            try {
              window.katex.render(props.data.formula, mathElement.value, {
                displayMode: props.data.display === 'block',
                throwOnError: false
              })
            } catch (error) {
              console.warn('数学公式渲染失败:', error)
              mathElement.value.textContent = `$${props.data.formula}$`
            }
          } else {
            if (mathElement.value) {
              mathElement.value.textContent = `$${props.data.formula}$`
            }
          }
        }

        // 使用 onMounted 和 onUpdated 生命周期钩子
        onMounted(() => {
          // 延迟渲染，确保DOM已经准备好
          nextTick(() => {
            renderMath()
          })
        })

        onUpdated(() => {
          nextTick(() => {
            renderMath()
          })
        })

        return {
          mathElement,
          renderMath
        }
      },
      render() {
        const mathClass = this.data.display === 'block' ? 'math-block' : 'math-inline'

        return h('div', { class: mathClass }, [
          h('span', { ref: 'mathElement' })
        ])
      }
    },

    // 标题组件 - 🚀 范式转移：使用渲染函数
    HeadingComponent: {
      props: ['data'],
      render() {
        const level = Math.min(this.data.level || 1, 6)
        const headingTag = `h${level}`
        const headingClass = `heading-element heading-${level}`

        return h(headingTag, { class: headingClass }, this.data.content)
      }
    },

    // 列表组件 - 🚀 范式转移：使用渲染函数
    ListComponent: {
      props: ['data'],
      render() {
        const listTag = this.data.list_type === 'ordered' ? 'ol' : 'ul'
        const listClass = `list-element list-${this.data.list_type || 'unordered'}`
        const items = this.data.items || []

        const listItems = items.map(item => h('li', item))

        return h(listTag, { class: listClass }, listItems)
      }
    },

    // 代码块组件 - 🚀 范式转移：使用渲染函数
    CodeBlockComponent: {
      props: ['data'],
      render() {
        const elements = []

        if (this.data.language) {
          elements.push(h('div', { class: 'code-language' }, this.data.language))
        }

        elements.push(h('pre', [h('code', this.data.content)]))

        return h('div', { class: 'code-block-element' }, elements)
      }
    },

    // 行内代码组件 - 🚀 范式转移：使用渲染函数
    CodeInlineComponent: {
      props: ['data'],
      render() {
        return h('code', { class: 'code-inline-element' }, this.data.content)
      }
    }
  }
}
</script>

<style scoped>
.structured-message {
  width: 100%;
}

.structured-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.legacy-content {
  line-height: 1.6;
}

/* 段落样式 */
.paragraph-element {
  margin: 8px 0;
  line-height: 1.6;
  color: #333;
}

/* 表格样式 */
.table-element {
  margin: 16px 0;
  overflow-x: auto;
}

.structured-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.structured-table th {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  font-size: 14px;
}

.structured-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  color: #555;
  font-size: 14px;
}

.structured-table tr:nth-child(even) {
  background-color: #f9f9f9;
}

.structured-table tr:hover {
  background-color: #f0f8ff;
}

/* 数学公式样式 */
.math-block {
  margin: 16px 0;
  text-align: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #007bff;
}

.math-inline {
  display: inline;
  margin: 0 2px;
}

/* 标题样式 */
.heading-element {
  margin: 20px 0 12px 0;
  color: #2c3e50;
  font-weight: 600;
}

.heading-1 { font-size: 28px; }
.heading-2 { font-size: 24px; }
.heading-3 { font-size: 20px; }
.heading-4 { font-size: 18px; }
.heading-5 { font-size: 16px; }
.heading-6 { font-size: 14px; }

/* 列表样式 */
.list-element {
  margin: 12px 0;
  padding-left: 20px;
}

.list-element li {
  margin: 4px 0;
  line-height: 1.5;
}

/* 代码样式 */
.code-block-element {
  margin: 16px 0;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e0e0e0;
}

.code-language {
  background: #f1f3f4;
  padding: 8px 16px;
  font-size: 12px;
  color: #666;
  border-bottom: 1px solid #e0e0e0;
}

.code-block-element pre {
  margin: 0;
  padding: 16px;
  background: #f8f9fa;
  overflow-x: auto;
}

.code-block-element code {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
}

.code-inline-element {
  background: #f1f3f4;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  color: #d63384;
}

/* 🎬 渐进式渲染动画样式 */
.progressive-content {
  position: relative;
}

.progressive-element {
  opacity: 0;
  transform: translateY(10px);
  animation: fadeInUp 0.5s ease-out forwards;
}

.progressive-element.animating {
  animation: fadeInUp 0.8s ease-out forwards;
}

/* 淡入向上动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Vue transition-group 动画 */
.fade-in-enter-active {
  transition: all 0.6s ease-out;
}

.fade-in-enter-from {
  opacity: 0;
  transform: translateY(30px) scale(0.95);
}

.fade-in-enter-to {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* 正在渲染指示器 */
.rendering-indicator {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 0;
  color: #666;
  font-size: 14px;
  opacity: 0.8;
}

/* 加载点动画 */
.loading-dots {
  display: flex;
  gap: 4px;
}

.loading-dots .dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #409eff;
  animation: dotPulse 1.4s infinite ease-in-out;
}

.loading-dots .dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots .dot:nth-child(2) {
  animation-delay: -0.16s;
}

.loading-dots .dot:nth-child(3) {
  animation-delay: 0s;
}

@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.rendering-text {
  font-style: italic;
  color: #409eff;
}

/* 特殊元素的渐进式动画优化 */
.progressive-element .structured-table {
  animation: tableSlideIn 0.8s ease-out forwards;
}

@keyframes tableSlideIn {
  from {
    opacity: 0;
    transform: translateX(-20px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

.progressive-element .math-element {
  animation: mathFadeIn 0.6s ease-out forwards;
}

@keyframes mathFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 来源标注样式 */
.element-source-attribution {
  margin-bottom: 8px;
  opacity: 0.9;
}

.progressive-element {
  position: relative;
}

.progressive-element:hover .element-source-attribution {
  opacity: 1;
}

/* 来源标注动画 */
.element-source-attribution {
  animation: sourceAttributionFadeIn 0.4s ease-out forwards;
}

@keyframes sourceAttributionFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 0.9;
    transform: translateY(0);
  }
}
</style>
