#!/usr/bin/env python3
"""
端到端测试：范式转移系统完整验证
测试从AI生成到前端渲染的完整流程
"""

import asyncio
import sys
import os
import json
import time

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from services.content_structure_parser import ContentStructureParser

def test_table_rendering_scenarios():
    """测试各种表格渲染场景"""
    print("📊 测试表格渲染场景...")
    
    parser = ContentStructureParser()
    
    # 场景1: 标准表格
    standard_table = """
| 故障类型 | 发生次数 | 严重程度 |
|---------|---------|---------|
| 叶片裂纹 | 15 | 高 |
| 表面磨损 | 8 | 中 |
"""
    
    # 场景2: 压缩表格（AI常生成的格式）
    compressed_table = "|| 组件 | 状态 | 建议 || 齿轮箱 | 正常 | 定期维护 || 发电机 | 异常 | 立即检查 ||"
    
    # 场景3: 混合内容（表格+数学公式）
    mixed_content = """
# 风机性能分析

## 功率计算

风机功率公式：$P = \\frac{1}{2} \\rho A v^3 C_p$

## 性能数据

| 风速(m/s) | 功率(kW) | 效率(%) |
|----------|----------|---------|
| 5 | 100 | 85 |
| 10 | 800 | 92 |
| 15 | 1500 | 88 |

其中功率系数 $C_p$ 的最大理论值为 0.593（贝茨极限）。
"""
    
    test_cases = [
        ("标准表格", standard_table),
        ("压缩表格", compressed_table), 
        ("混合内容", mixed_content)
    ]
    
    results = []
    
    for name, content in test_cases:
        try:
            print(f"\n🧪 测试 {name}...")
            structured = parser.parse_markdown_to_structure(content)
            
            # 统计各类型元素
            element_counts = {}
            for element in structured:
                element_type = element['type']
                element_counts[element_type] = element_counts.get(element_type, 0) + 1
            
            print(f"   ✅ 解析成功: {element_counts}")
            
            # 检查表格解析
            tables = [e for e in structured if e['type'] == 'table']
            if tables:
                table = tables[0]
                print(f"   📊 表格: {len(table['headers'])} 列 x {len(table['rows'])} 行")
                print(f"   📋 表头: {table['headers']}")
            
            # 检查数学公式解析
            math_elements = [e for e in structured if e['type'] == 'math']
            if math_elements:
                print(f"   🧮 数学公式: {len(math_elements)} 个")
                for math_elem in math_elements:
                    print(f"      {math_elem['display']}: {math_elem['formula'][:30]}...")
            
            results.append((name, True, structured))
            
        except Exception as e:
            print(f"   ❌ 解析失败: {e}")
            results.append((name, False, str(e)))
    
    return results

def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n🔄 测试向后兼容性...")
    
    # 模拟新格式响应
    new_format_response = {
        "type": "structured_response",
        "content": [
            {"type": "paragraph", "content": "这是新格式的段落"},
            {"type": "table", "headers": ["列1", "列2"], "rows": [["数据1", "数据2"]]}
        ],
        "raw_markdown": "原始Markdown内容",
        "timestamp": time.time()
    }
    
    # 模拟旧格式响应
    old_format_response = "这是传统的Markdown字符串响应"
    
    # 测试响应类型检测
    def detect_response_type(response):
        if isinstance(response, dict) and response.get("type") == "structured_response":
            return "structured"
        elif isinstance(response, str):
            return "legacy"
        else:
            return "unknown"
    
    new_type = detect_response_type(new_format_response)
    old_type = detect_response_type(old_format_response)
    
    print(f"   新格式检测: {new_type} {'✅' if new_type == 'structured' else '❌'}")
    print(f"   旧格式检测: {old_type} {'✅' if old_type == 'legacy' else '❌'}")
    
    return new_type == "structured" and old_type == "legacy"

def test_frontend_component_mapping():
    """测试前端组件映射"""
    print("\n🎨 测试前端组件映射...")
    
    # 模拟前端组件映射逻辑
    component_map = {
        'paragraph': 'ParagraphComponent',
        'table': 'TableComponent', 
        'math': 'MathComponent',
        'heading': 'HeadingComponent',
        'list': 'ListComponent',
        'code_block': 'CodeBlockComponent',
        'code_inline': 'CodeInlineComponent'
    }
    
    # 测试所有元素类型
    test_elements = [
        {"type": "paragraph", "content": "段落内容"},
        {"type": "table", "headers": ["A", "B"], "rows": [["1", "2"]]},
        {"type": "math", "formula": "E=mc^2", "display": "inline"},
        {"type": "heading", "content": "标题", "level": 2},
        {"type": "list", "items": ["项目1", "项目2"], "list_type": "unordered"},
        {"type": "code_block", "content": "print('hello')", "language": "python"},
        {"type": "code_inline", "content": "console.log()"}
    ]
    
    mapping_success = True
    for element in test_elements:
        element_type = element['type']
        expected_component = component_map.get(element_type, 'ParagraphComponent')
        
        if element_type in component_map:
            print(f"   ✅ {element_type} -> {expected_component}")
        else:
            print(f"   ❌ {element_type} -> 未知组件")
            mapping_success = False
    
    return mapping_success

def simulate_websocket_flow():
    """模拟WebSocket数据流"""
    print("\n🌐 模拟WebSocket数据流...")
    
    # 模拟AI生成的结构化响应
    ai_response = {
        "type": "structured_response",
        "content": [
            {"type": "heading", "content": "风机故障诊断结果", "level": 1},
            {"type": "paragraph", "content": "根据您提供的信息，我分析了以下故障情况："},
            {
                "type": "table", 
                "headers": ["故障类型", "严重程度", "建议措施"],
                "rows": [
                    ["叶片裂纹", "高", "立即停机检修"],
                    ["齿轮箱异响", "中", "安排维护计划"],
                    ["发电机温度高", "低", "加强监控"]
                ]
            },
            {"type": "paragraph", "content": "请根据以上建议及时处理相关故障。"}
        ],
        "raw_markdown": "# 风机故障诊断结果\n\n根据您提供的信息...",
        "timestamp": time.time()
    }
    
    # 模拟WebSocket发送
    def simulate_websocket_send(data):
        print(f"   📤 WebSocket发送: {data['type']}")
        print(f"   📊 内容元素: {len(data['content'])} 个")
        
        # 检查每个元素
        for i, element in enumerate(data['content']):
            print(f"      元素{i+1}: {element['type']}")
            if element['type'] == 'table':
                print(f"         表格: {len(element['headers'])} 列 x {len(element['rows'])} 行")
        
        return True
    
    # 模拟前端接收
    def simulate_frontend_receive(data):
        print(f"   📥 前端接收: {data['type']}")
        
        # 检查是否为结构化响应
        if data.get('type') == 'structured_response':
            print("   🎨 使用StructuredMessageRenderer渲染")
            return "structured_rendering"
        else:
            print("   📝 使用传统formatMessageText渲染")
            return "legacy_rendering"
    
    # 执行模拟流程
    send_success = simulate_websocket_send(ai_response)
    render_type = simulate_frontend_receive(ai_response)
    
    success = send_success and render_type == "structured_rendering"
    print(f"   {'✅' if success else '❌'} WebSocket流程模拟{'成功' if success else '失败'}")
    
    return success

def main():
    """主测试函数"""
    print("🚀 范式转移系统 - 端到端测试")
    print("=" * 80)
    
    # 测试1: 表格渲染场景
    table_results = test_table_rendering_scenarios()
    table_success = all(result[1] for result in table_results)
    
    # 测试2: 向后兼容性
    compatibility_success = test_backward_compatibility()
    
    # 测试3: 前端组件映射
    mapping_success = test_frontend_component_mapping()
    
    # 测试4: WebSocket数据流
    websocket_success = simulate_websocket_flow()
    
    # 总结报告
    print("\n" + "=" * 80)
    print("📊 端到端测试总结:")
    print(f"   表格渲染场景: {'✅ 通过' if table_success else '❌ 失败'}")
    print(f"   向后兼容性: {'✅ 通过' if compatibility_success else '❌ 失败'}")
    print(f"   前端组件映射: {'✅ 通过' if mapping_success else '❌ 失败'}")
    print(f"   WebSocket数据流: {'✅ 通过' if websocket_success else '❌ 失败'}")
    
    overall_success = all([table_success, compatibility_success, mapping_success, websocket_success])
    
    print(f"\n🎯 整体测试结果: {'✅ 全部通过' if overall_success else '❌ 部分失败'}")
    
    if overall_success:
        print("\n🎉 范式转移系统端到端测试成功！")
        print("✨ 系统已准备好处理AI生成的表格和混合内容")
        print("🚀 可以启动前端进行实际测试")
    else:
        print("\n⚠️ 部分测试失败，需要进一步调试")
    
    return overall_success

if __name__ == "__main__":
    main()
