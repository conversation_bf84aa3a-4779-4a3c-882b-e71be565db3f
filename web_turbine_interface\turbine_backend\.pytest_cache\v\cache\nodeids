["tests/test_context_performance_analysis.py::TestContextPerformanceAnalysis::test_session_manager_memory_efficiency", "tests/test_context_performance_playwright.py::TestContextPerformancePlaywright::test_concurrent_context_performance", "tests/test_context_performance_playwright.py::TestContextPerformancePlaywright::test_context_management_performance_simulation", "tests/test_context_performance_simple.py::TestContextPerformanceSimple::test_intelligent_router_performance", "tests/test_context_performance_simple.py::TestContextPerformanceSimple::test_semantic_analyzer_performance", "tests/test_context_performance_simple.py::TestContextPerformanceSimple::test_system_resource_usage", "tests/test_intelligent_router_tdd.py::TestIntelligentRouterTDD::test_all_six_tools_routing", "tests/test_intelligent_router_tdd.py::TestIntelligentRouterTDD::test_analyze_query_intent_fault_diagnosis", "tests/test_intelligent_router_tdd.py::TestIntelligentRouterTDD::test_analyze_query_intent_online_search", "tests/test_intelligent_router_tdd.py::TestIntelligentRouterTDD::test_analyze_query_intent_operation_guidance", "tests/test_intelligent_router_tdd.py::TestIntelligentRouterTDD::test_confidence_score_validity", "tests/test_intelligent_router_tdd.py::TestIntelligentRouterTDD::test_consistency_same_query", "tests/test_intelligent_router_tdd.py::TestIntelligentRouterTDD::test_context_aware_routing", "tests/test_intelligent_router_tdd.py::TestIntelligentRouterTDD::test_empty_query", "tests/test_intelligent_router_tdd.py::TestIntelligentRouterTDD::test_get_tool_recommendations_structure", "tests/test_intelligent_router_tdd.py::TestIntelligentRouterTDD::test_logging_functionality", "tests/test_intelligent_router_tdd.py::TestIntelligentRouterTDD::test_math_formula_query_routing", "tests/test_intelligent_router_tdd.py::TestIntelligentRouterTDD::test_memory_usage", "tests/test_intelligent_router_tdd.py::TestIntelligentRouterTDD::test_mixed_language_query", "tests/test_intelligent_router_tdd.py::TestIntelligentRouterTDD::test_non_string_query", "tests/test_intelligent_router_tdd.py::TestIntelligentRouterTDD::test_none_query", "tests/test_intelligent_router_tdd.py::TestIntelligentRouterTDD::test_performance_batch_queries", "tests/test_intelligent_router_tdd.py::TestIntelligentRouterTDD::test_performance_single_query", "tests/test_intelligent_router_tdd.py::TestIntelligentRouterTDD::test_router_initialization", "tests/test_intelligent_router_tdd.py::TestIntelligentRouterTDD::test_source_attribution_support", "tests/test_intelligent_router_tdd.py::TestIntelligentRouterTDD::test_special_characters_query", "tests/test_intelligent_router_tdd.py::TestIntelligentRouterTDD::test_very_long_query", "tests/test_intelligent_router_tdd.py::TestWebSocketAdapterTDD::test_adapter_initialization", "tests/test_intelligent_router_tdd.py::TestWebSocketAdapterTDD::test_get_tool_recommendations_integration", "tests/test_tool_selection_logic.py::TestToolSelectionLogic::test_agent_service_integration", "tests/test_tool_selection_logic.py::TestToolSelectionLogic::test_handle_ai_only_mode_selection", "tests/test_tool_selection_logic.py::TestToolSelectionLogic::test_handle_all_mode_selection", "tests/test_tool_selection_logic.py::TestToolSelectionLogic::test_handle_manual_mode_selection", "tests/test_tool_selection_logic.py::TestToolSelectionLogic::test_handle_required_mode_selection", "tests/test_tool_selection_logic.py::TestToolSelectionLogic::test_invalid_tool_combination", "tests/test_tool_selection_logic.py::TestToolSelectionLogic::test_multiple_tool_selection_performance", "tests/test_tool_selection_logic.py::TestToolSelectionLogic::test_parallel_tool_execution", "tests/test_tool_selection_logic.py::TestToolSelectionLogic::test_session_not_found_error", "tests/test_tool_selection_logic.py::TestToolSelectionLogic::test_tool_combination_validation", "tests/test_tool_selection_logic.py::TestToolSelectionLogic::test_tool_execution_error_handling", "tests/test_tool_selection_logic.py::TestToolSelectionLogic::test_websocket_message_format_compatibility"]