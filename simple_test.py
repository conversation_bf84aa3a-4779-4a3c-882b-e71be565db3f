#!/usr/bin/env python3
"""
简单测试：验证基本功能
"""

import asyncio
import sys
import os

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'web_turbine_interface/turbine_backend/app/services'))

async def test_basic_functionality():
    """测试基本功能"""
    print("🧪 开始基本功能测试")
    
    try:
        # 测试SessionManager
        from session_manager import SessionManager
        
        session_manager = SessionManager()
        print("✅ SessionManager导入成功")
        
        # 创建会话
        session_id = "test_001"
        session = await session_manager.create_session(session_id)
        print(f"✅ 创建会话成功: {session.session_id}")
        
        # 添加消息
        await session_manager.add_message(
            session_id=session_id,
            user_query="测试查询",
            ai_response="测试回答",
            tool_results={},
            source_attribution={}
        )
        print("✅ 添加消息成功")
        
        # 获取上下文
        context = await session_manager.get_conversation_context(session_id)
        print(f"✅ 获取上下文成功: {len(context['recent_messages'])} 条消息")
        
        # 测试WebSocket适配器
        from langgraph_websocket_adapter import LangGraphWebSocketAdapter
        
        adapter = LangGraphWebSocketAdapter()
        print("✅ WebSocket适配器创建成功")
        
        print("🎉 基本功能测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_basic_functionality())
