#!/usr/bin/env python3
"""
上下文管理系统性能分析测试套件
基于C4任务完成状态，深入分析SessionManager、IntelligentMemoryManager、AdaptiveSemanticRouter的性能表现
"""

import unittest
import sys
import os
import time
import asyncio
import psutil
import tracemalloc
from unittest.mock import Mock, patch
from datetime import datetime, timedelta
import json

# 添加应用路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

class TestContextPerformanceAnalysis(unittest.TestCase):
    """上下文管理系统性能分析测试套件"""
    
    def setUp(self):
        """测试前置设置"""
        # 启动内存跟踪
        tracemalloc.start()
        self.start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        self.start_time = time.time()
        
    def tearDown(self):
        """测试后置清理"""
        # 停止内存跟踪
        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        
        end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        end_time = time.time()
        
        print(f"\n📊 性能指标:")
        print(f"   内存使用: {end_memory - self.start_memory:.2f} MB")
        print(f"   执行时间: {end_time - self.start_time:.3f} 秒")
        print(f"   峰值内存: {peak / 1024 / 1024:.2f} MB")

    # ==================== 内存使用效率分析 ====================
    
    def test_session_manager_memory_efficiency(self):
        """测试SessionManager的内存使用效率"""
        try:
            # 先设置事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            from app.services.session_manager import SessionManager, MessageContext

            # 创建SessionManager实例（在事件循环中）
            session_manager = SessionManager(max_sessions=10, max_messages_per_session=20)
            
            # 模拟多个会话的创建和使用
            session_ids = []
            for i in range(10):
                session_id = f"test_session_{i}"
                session_ids.append(session_id)
                
                # 创建会话（使用已有的事件循环）
                session = loop.run_until_complete(session_manager.create_session(session_id))
                
                # 添加多条消息
                for j in range(20):
                    user_query = f"测试查询 {j}: 风机叶片故障分析"
                    ai_response = f"这是第{j}条AI回答，包含详细的技术分析内容..." * 10
                    tool_results = {"local-knowledge-base": f"工具结果{j}"}
                    
                    loop.run_until_complete(session_manager.add_message(
                        session_id, user_query, ai_response, tool_results
                    ))

            # 关闭事件循环
            loop.close()
            
            # 验证内存使用合理性
            self.assertEqual(len(session_manager.sessions), 10)
            
            # 验证消息历史限制生效
            for session_id in session_ids:
                session = session_manager.sessions[session_id]
                # 每个会话应该有40条消息（20个用户+20个AI）
                self.assertEqual(len(session.message_history), 40)
            
            print(f"✅ SessionManager内存效率测试通过")
            print(f"   管理会话数: {len(session_manager.sessions)}")
            print(f"   每会话消息数: {len(session_manager.sessions[session_ids[0]].message_history)}")
            
        except ImportError as e:
            self.skipTest(f"SessionManager模块不可用: {e}")
    
    def test_session_cleanup_mechanism(self):
        """测试会话清理机制的效率"""
        try:
            from app.services.session_manager import SessionManager
            
            session_manager = SessionManager(max_sessions=5, max_messages_per_session=10)
            
            # 创建超过限制的会话数
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            for i in range(8):  # 超过max_sessions=5
                session_id = f"cleanup_test_{i}"
                loop.run_until_complete(session_manager.create_session(session_id))
            
            loop.close()
            
            # 验证清理机制生效
            self.assertLessEqual(len(session_manager.sessions), 5)
            print(f"✅ 会话清理机制测试通过，当前会话数: {len(session_manager.sessions)}")
            
        except ImportError as e:
            self.skipTest(f"SessionManager模块不可用: {e}")

    # ==================== 语义分析性能测试 ====================
    
    def test_hybrid_semantic_analyzer_performance(self):
        """测试混合语义分析器的性能"""
        try:
            from memory_enhancement.hybrid_semantic_analyzer import HybridSemanticAnalyzer
            
            analyzer = HybridSemanticAnalyzer()
            
            # 测试不同复杂度的查询
            test_queries = [
                "风机故障",  # 简单查询
                "风机叶片断裂的原因分析和预防措施",  # 中等复杂度
                "请详细分析风机齿轮箱轴承疲劳失效的机理，包括载荷分析、材料特性、润滑条件对轴承寿命的影响，并提供相应的预防性维护策略和状态监测方案"  # 复杂查询
            ]
            
            performance_results = []
            
            for query in test_queries:
                start_time = time.time()
                result = analyzer.analyze_query(query)
                end_time = time.time()
                
                performance_results.append({
                    "query_length": len(query),
                    "analysis_method": result.analysis_method,
                    "processing_time": end_time - start_time,
                    "confidence_score": result.confidence_score
                })
                
                # 验证结果质量
                self.assertIsNotNone(result.core_concept)
                self.assertIsNotNone(result.technical_domain)
                self.assertGreater(len(result.semantic_keywords), 0)
            
            # 分析性能趋势
            avg_basic_time = sum(r["processing_time"] for r in performance_results if r["analysis_method"] == "basic") / max(1, sum(1 for r in performance_results if r["analysis_method"] == "basic"))
            avg_advanced_time = sum(r["processing_time"] for r in performance_results if r["analysis_method"] == "advanced") / max(1, sum(1 for r in performance_results if r["analysis_method"] == "advanced"))
            
            print(f"✅ 语义分析性能测试完成")
            print(f"   基础分析平均时间: {avg_basic_time:.3f}秒")
            print(f"   高级分析平均时间: {avg_advanced_time:.3f}秒")
            
            # 性能要求验证
            for result in performance_results:
                self.assertLess(result["processing_time"], 1.0, "语义分析时间应小于1秒")
            
        except ImportError as e:
            self.skipTest(f"HybridSemanticAnalyzer模块不可用: {e}")

    # ==================== 记忆检索效率评估 ====================
    
    def test_memory_manager_retrieval_performance(self):
        """测试智能记忆管理器的检索性能"""
        try:
            from memory_enhancement.intelligent_memory_manager import IntelligentMemoryManager
            from memory_enhancement.hybrid_semantic_analyzer import HybridSemanticAnalyzer
            
            # 创建临时内存管理器
            memory_manager = IntelligentMemoryManager("test_memory.json")
            analyzer = HybridSemanticAnalyzer()
            
            # 预填充一些记忆数据
            test_memories = [
                ("风机叶片断裂分析", "叶片断裂通常由疲劳载荷引起..."),
                ("齿轮箱故障诊断", "齿轮箱故障主要表现为异常振动..."),
                ("发电机维护指南", "发电机需要定期检查绕组绝缘..."),
                ("控制系统优化", "控制系统优化需要考虑响应速度..."),
                ("风机性能评估", "风机性能评估包括功率曲线分析...")
            ]
            
            # 存储测试记忆
            for query, response in test_memories:
                semantic_result = analyzer.analyze_query(query)
                interaction_context = {"tools_used": ["local-knowledge-base"], "timestamp": datetime.now().isoformat()}
                memory_manager.store_memory(semantic_result, query, response, interaction_context)
            
            # 测试检索性能
            search_queries = [
                "叶片问题分析",
                "齿轮箱异常",
                "发电机检查",
                "系统优化方案"
            ]
            
            retrieval_times = []
            for search_query in search_queries:
                semantic_result = analyzer.analyze_query(search_query)
                
                start_time = time.time()
                relevant_memories = memory_manager.retrieve_relevant_memories(semantic_result, limit=3)
                end_time = time.time()
                
                retrieval_times.append(end_time - start_time)
                
                # 验证检索结果
                self.assertIsInstance(relevant_memories, list)
                print(f"   查询'{search_query}': 找到{len(relevant_memories)}条相关记忆")
            
            avg_retrieval_time = sum(retrieval_times) / len(retrieval_times)
            print(f"✅ 记忆检索性能测试完成")
            print(f"   平均检索时间: {avg_retrieval_time:.3f}秒")
            print(f"   总记忆数量: {len(memory_manager.memories)}")
            
            # 性能要求验证
            self.assertLess(avg_retrieval_time, 0.1, "记忆检索时间应小于0.1秒")
            
            # 清理测试文件
            if os.path.exists("test_memory.json"):
                os.remove("test_memory.json")
                
        except ImportError as e:
            self.skipTest(f"IntelligentMemoryManager模块不可用: {e}")

    # ==================== 综合性能基准测试 ====================
    
    def test_integrated_context_performance_benchmark(self):
        """综合上下文管理性能基准测试"""
        print(f"\n🚀 开始综合性能基准测试...")
        
        # 模拟真实使用场景的性能测试
        test_scenarios = [
            {
                "name": "简单查询场景",
                "queries": ["风机状态", "故障检查", "维护计划"],
                "expected_time": 0.5
            },
            {
                "name": "复杂分析场景", 
                "queries": [
                    "风机叶片疲劳分析的详细方法和预防措施",
                    "齿轮箱振动异常的根本原因分析和解决方案",
                    "发电机绕组温度过高的诊断流程和维修指导"
                ],
                "expected_time": 2.0
            }
        ]
        
        benchmark_results = {}
        
        for scenario in test_scenarios:
            scenario_times = []
            
            for query in scenario["queries"]:
                start_time = time.time()
                
                # 模拟完整的上下文处理流程
                try:
                    from memory_enhancement.hybrid_semantic_analyzer import HybridSemanticAnalyzer
                    analyzer = HybridSemanticAnalyzer()
                    semantic_result = analyzer.analyze_query(query)
                    
                    # 模拟会话管理
                    from app.services.session_manager import SessionManager
                    session_manager = SessionManager()
                    
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    context = loop.run_until_complete(
                        session_manager.get_conversation_context("benchmark_test", current_query=query)
                    )
                    loop.close()
                    
                except ImportError:
                    # 如果模块不可用，使用模拟时间
                    time.sleep(0.01)
                
                end_time = time.time()
                scenario_times.append(end_time - start_time)
            
            avg_time = sum(scenario_times) / len(scenario_times)
            benchmark_results[scenario["name"]] = {
                "average_time": avg_time,
                "max_time": max(scenario_times),
                "expected_time": scenario["expected_time"],
                "performance_ratio": scenario["expected_time"] / avg_time if avg_time > 0 else float('inf')
            }
        
        # 输出基准测试结果
        print(f"\n📊 综合性能基准测试结果:")
        for scenario_name, results in benchmark_results.items():
            print(f"   {scenario_name}:")
            print(f"     平均时间: {results['average_time']:.3f}秒")
            print(f"     最大时间: {results['max_time']:.3f}秒") 
            print(f"     性能比率: {results['performance_ratio']:.2f}x")
            
            # 性能验证
            self.assertLess(results['average_time'], results['expected_time'], 
                           f"{scenario_name}性能不达标")

if __name__ == '__main__':
    # 设置详细输出
    unittest.main(verbosity=2)
