#!/usr/bin/env python3
"""
测试AI处理功能
"""

import sys
import os
import asyncio
import json

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.dirname(__file__))
src_path = os.path.join(project_root, 'src')
if src_path not in sys.path:
    sys.path.insert(0, src_path)

async def test_ai_processing():
    """测试AI处理功能"""
    try:
        print("🔧 开始测试AI处理功能...")
        
        # 导入必要的模块
        from interactive_langgraph_agent import InteractiveLangGraphAgent
        from config import get_config
        
        print("✅ 成功导入模块")
        
        # 获取配置
        config = get_config()
        print("✅ 成功获取配置")
        
        # 创建agent实例
        agent = InteractiveLangGraphAgent(config)
        print("✅ 成功创建agent实例")
        
        # 测试简单的AI查询
        test_query = "测试导入修复：请生成一个简单的风机故障表格"
        print(f"🤖 测试查询: {test_query}")
        
        # 创建初始状态
        initial_state = {
            "user_query": test_query,
            "session_id": "test_session",
            "conversation_history": [],
            "selected_tools": [],
            "tool_results": {},
            "final_response": None,
            "routing_decision": "ai_only",
            "analysis_type": "故障诊断"
        }
        
        print("🔄 开始处理查询...")
        
        # 调用AI节点
        result = await agent._process_with_ai_node(initial_state)
        
        print("✅ AI处理完成")
        print(f"📊 结果类型: {type(result.get('final_response'))}")
        
        if isinstance(result.get('final_response'), dict):
            response_type = result['final_response'].get('type', 'unknown')
            print(f"📋 响应类型: {response_type}")
            
            if response_type == "structured_response":
                content = result['final_response'].get('content', [])
                print(f"📝 结构化内容元素数量: {len(content)}")
                
                # 检查是否包含表格
                table_count = sum(1 for item in content if item.get('type') == 'table')
                print(f"📊 表格数量: {table_count}")
                
                if table_count > 0:
                    print("🎉 成功生成结构化表格内容！")
                    
                    # 显示第一个表格的结构
                    for item in content:
                        if item.get('type') == 'table':
                            table_data = item.get('content', {})
                            headers = table_data.get('headers', [])
                            rows = table_data.get('rows', [])
                            print(f"📋 表格标题: {headers}")
                            print(f"📊 数据行数: {len(rows)}")
                            break
                else:
                    print("⚠️ 未检测到表格内容")
            else:
                print(f"📝 传统响应: {result['final_response'][:200]}...")
        else:
            print(f"📝 响应内容: {str(result.get('final_response'))[:200]}...")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 启动AI处理测试...")
    success = asyncio.run(test_ai_processing())
    
    if success:
        print("\n🎉 测试成功完成！")
        print("✅ 导入修复有效")
        print("✅ AI处理功能正常")
        print("✅ 结构化解析器工作正常")
    else:
        print("\n❌ 测试失败")
        print("需要进一步调试API配置问题")
