"""
风机智能体 Web界面 - FastAPI后端主应用
基于Context7最佳实践构建，支持WebSocket实时通信
"""

from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import json
import asyncio
from typing import List, Dict, Any
import logging
from datetime import datetime

# 导入智能体服务
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '..'))

from services.agent_service import agent_service

# 导入友好错误处理器
from utils.error_handler import error_handler

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 异步任务：处理智能体查询，避免阻塞消息接收循环
async def process_query_task(agent_service, user_message: str, client_id: str, websocket: WebSocket, manager):
    """
    异步处理智能体查询任务 - 修复ASGI错误
    这样可以避免阻塞WebSocket消息接收循环，确保能同时接收工具选择消息
    """
    try:
        async for response in agent_service.process_query(user_message, client_id):
            # 检查连接是否仍然有效再发送消息
            if websocket in manager.active_connections:
                success = await manager.send_personal_message(response, websocket)
                if not success:
                    logger.warning(f"客户端 {client_id} 连接已断开，停止发送响应")
                    break
            else:
                logger.warning(f"客户端 {client_id} 连接已断开，停止处理查询")
                break
    except Exception as e:
        logger.error(f"处理查询任务时出错: {e}")

        # 只有在连接仍然有效时才发送错误消息
        if websocket in manager.active_connections:
            # 使用友好错误处理
            friendly_error = error_handler.format_error_for_websocket(
                f"处理查询时出错: {str(e)}",
                "service_unavailable"
            )
            await manager.send_personal_message(friendly_error, websocket)

# 创建FastAPI应用实例
app = FastAPI(
    title="风机智能体 Web API",
    description="基于FastAPI的风机智能体Web界面后端服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS - 允许前端跨域访问
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],  # Vue前端地址
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# WebSocket连接管理器
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.connection_info: Dict[WebSocket, Dict[str, Any]] = {}

    async def connect(self, websocket: WebSocket, client_id: str = None):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections.append(websocket)
        self.connection_info[websocket] = {
            "client_id": client_id or f"client_{len(self.active_connections)}",
            "connected_at": datetime.now().isoformat(),
            "message_count": 0
        }
        logger.info(f"客户端 {self.connection_info[websocket]['client_id']} 已连接")
        
        # 发送欢迎消息
        await self.send_personal_message({
            "type": "system",
            "message": f"🌪️ 欢迎使用风机智能体 Web版！",
            "client_id": self.connection_info[websocket]['client_id'],
            "timestamp": datetime.now().isoformat()
        }, websocket)

    def disconnect(self, websocket: WebSocket):
        """断开WebSocket连接"""
        if websocket in self.active_connections:
            client_info = self.connection_info.get(websocket, {})
            client_id = client_info.get("client_id", "unknown")
            logger.info(f"客户端 {client_id} 已断开连接")
            
            self.active_connections.remove(websocket)
            if websocket in self.connection_info:
                del self.connection_info[websocket]

    async def send_personal_message(self, message: Dict[str, Any], websocket: WebSocket):
        """发送个人消息 - 修复ASGI错误"""
        # 检查连接是否仍然有效
        if websocket not in self.active_connections:
            logger.warning("尝试向已断开的WebSocket连接发送消息，跳过发送")
            return False

        # 检查WebSocket状态
        try:
            # 检查WebSocket的客户端状态
            if websocket.client_state.name != "CONNECTED":
                logger.warning(f"WebSocket连接状态异常: {websocket.client_state.name}，跳过发送")
                return False
        except AttributeError:
            # 如果无法获取状态，尝试发送但做好异常处理
            pass

        try:
            await websocket.send_text(json.dumps(message, ensure_ascii=False))
            if websocket in self.connection_info:
                self.connection_info[websocket]["message_count"] += 1
            return True
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            # 如果发送失败，从活动连接中移除该WebSocket
            if websocket in self.active_connections:
                logger.info("由于发送失败，从活动连接中移除WebSocket")
                self.disconnect(websocket)
            return False

    async def broadcast(self, message: Dict[str, Any]):
        """广播消息给所有连接的客户端 - 修复ASGI错误"""
        if not self.active_connections:
            return

        message["timestamp"] = datetime.now().isoformat()
        disconnected = []

        for connection in self.active_connections:
            # 检查连接状态
            try:
                if connection.client_state.name != "CONNECTED":
                    logger.warning(f"跳过状态异常的WebSocket连接: {connection.client_state.name}")
                    disconnected.append(connection)
                    continue
            except AttributeError:
                # 如果无法获取状态，尝试发送
                pass

            try:
                await connection.send_text(json.dumps(message, ensure_ascii=False))
            except Exception as e:
                logger.error(f"广播消息失败: {e}")
                disconnected.append(connection)

        # 清理断开的连接
        for connection in disconnected:
            self.disconnect(connection)

    def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        return {
            "total_connections": len(self.active_connections),
            "connections": [
                {
                    "client_id": info["client_id"],
                    "connected_at": info["connected_at"],
                    "message_count": info["message_count"]
                }
                for info in self.connection_info.values()
            ]
        }

# 全局连接管理器实例
manager = ConnectionManager()

# 基础HTTP路由
@app.get("/")
async def root():
    """根路径 - 系统状态"""
    return {
        "message": "🌪️ 风机智能体 Web API 服务正在运行",
        "version": "1.0.0",
        "status": "active",
        "timestamp": datetime.now().isoformat(),
        "websocket_endpoint": "/ws",
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "connections": manager.get_connection_stats()
    }

@app.get("/api/status")
async def api_status():
    """API状态信息"""
    agent_status = await agent_service.get_system_status()

    return {
        "api_version": "1.0.0",
        "service": "风机智能体 Web API",
        "features": [
            "WebSocket实时通信",
            "风机数据查询",
            "智能分析服务",
            "流式AI回答",
            "智能体集成"
        ],
        "endpoints": {
            "websocket": "/ws",
            "health": "/health",
            "docs": "/docs",
            "agent_status": "/api/agent/status",
            "tools": "/api/tools"
        },
        "connection_stats": manager.get_connection_stats(),
        "agent_status": agent_status
    }

@app.get("/api/agent/status")
async def get_agent_status():
    """获取智能体状态"""
    return await agent_service.get_system_status()

@app.get("/api/tools")
async def get_available_tools():
    """获取可用工具列表"""
    return {
        "tools": await agent_service.get_available_tools(),
        "timestamp": datetime.now().isoformat()
    }

# WebSocket端点
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """主WebSocket端点 - 处理实时通信"""
    client_id = None
    try:
        # 建立连接
        await manager.connect(websocket)
        client_id = manager.connection_info[websocket]["client_id"]
        
        # 消息处理循环
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            logger.info(f"收到来自 {client_id} 的消息: {data}")

            try:
                # 解析JSON消息
                message = json.loads(data)
                message_type = message.get("type", "unknown")
                logger.info(f"解析消息类型: {message_type}")
                
                # 根据消息类型处理
                if message_type == "ping":
                    # 心跳检测
                    await manager.send_personal_message({
                        "type": "pong",
                        "message": "连接正常",
                        "timestamp": datetime.now().isoformat()
                    }, websocket)
                    
                elif message_type == "chat":
                    # 聊天消息 - 使用智能体处理
                    user_message = message.get("message", "")

                    # 发送用户消息确认
                    await manager.send_personal_message({
                        "type": "message_received",
                        "message": f"收到您的消息，正在处理...",
                        "original_message": user_message,
                        "timestamp": datetime.now().isoformat()
                    }, websocket)

                    # 使用异步任务处理查询，避免阻塞消息接收循环
                    asyncio.create_task(process_query_task(agent_service, user_message, client_id, websocket, manager))
                    
                elif message_type == "query":
                    # 风机查询请求 - 使用智能体处理
                    query = message.get("query", "")

                    # 使用异步任务处理查询，避免阻塞消息接收循环
                    asyncio.create_task(process_query_task(agent_service, query, client_id, websocket, manager))

                elif message_type == "tool_selection":
                    # 工具选择消息 - Human-in-the-Loop机制
                    selected_tools = message.get("selected_tools", [])
                    selection_mode = message.get("mode", "manual")  # 新增：获取选择模式
                    frontend_session_id = message.get("session_id", client_id)

                    logger.info(f"收到工具选择: {selected_tools} (模式: {selection_mode}) from {client_id} (前端会话ID: {frontend_session_id})")

                    # 将用户选择传递给LangGraphWebSocketAdapter
                    # 注意：LangGraph适配器使用client_id作为会话标识
                    choice_data = {
                        "selected_tools": selected_tools,
                        "mode": selection_mode,  # 新增：包含选择模式
                        "timestamp": datetime.now().isoformat()
                    }

                    # 通过智能体服务处理用户选择，使用client_id作为会话标识
                    success = agent_service.handle_user_choice(client_id, choice_data)

                    if success:
                        # 根据选择模式生成不同的确认消息
                        if selection_mode == "ai-only":
                            confirmation_message = "✅ 已确认选择：仅使用AI分析"
                            tools_display = "AI分析"
                        elif selection_mode == "required":
                            confirmation_message = f"✅ 已确认选择工具: {', '.join(selected_tools)} (必需工具)"
                            tools_display = ', '.join(selected_tools)
                        elif selection_mode == "all":
                            confirmation_message = f"✅ 已确认选择工具: {', '.join(selected_tools)} (全部推荐)"
                            tools_display = ', '.join(selected_tools)
                        else:  # manual
                            confirmation_message = f"✅ 已确认选择工具: {', '.join(selected_tools)} (手动选择)"
                            tools_display = ', '.join(selected_tools)

                        await manager.send_personal_message({
                            "type": "tool_selection_confirmed",
                            "message": confirmation_message,
                            "selected_tools": selected_tools,
                            "mode": selection_mode,  # 新增：包含选择模式
                            "tools_display": tools_display,  # 新增：用于显示的工具信息
                            "session_id": client_id,  # 使用client_id保持一致性
                            "frontend_session_id": frontend_session_id,  # 保留前端会话ID用于调试
                            "timestamp": datetime.now().isoformat()
                        }, websocket)
                    else:
                        logger.warning(f"工具选择处理失败: session {client_id} 不存在待处理的选择")

                        # 发送错误消息给前端
                        await manager.send_personal_message({
                            "type": "tool_selection_error",
                            "message": "❌ 工具选择处理失败：会话不存在或已过期",
                            "session_id": client_id,
                            "timestamp": datetime.now().isoformat()
                        }, websocket)

                else:
                    # 未知消息类型
                    await manager.send_personal_message({
                        "type": "error",
                        "message": f"未知的消息类型: {message_type}",
                        "timestamp": datetime.now().isoformat()
                    }, websocket)
                    
            except json.JSONDecodeError:
                # JSON解析错误 - 使用友好错误处理
                friendly_error = error_handler.format_error_for_websocket(
                    "消息格式错误，请发送有效的JSON",
                    "data_format"
                )
                await manager.send_personal_message(friendly_error, websocket)
                
    except WebSocketDisconnect:
        # 客户端断开连接
        manager.disconnect(websocket)
        if client_id:
            logger.info(f"客户端 {client_id} 主动断开连接")
            # 清理该客户端的待处理用户选择
            agent_service.cleanup_pending_choices(client_id)
    except Exception as e:
        # 其他异常
        logger.error(f"WebSocket连接异常: {e}")
        manager.disconnect(websocket)
        if client_id:
            # 清理该客户端的待处理用户选择
            agent_service.cleanup_pending_choices(client_id)

if __name__ == "__main__":
    import uvicorn
    print("🚀 启动风机智能体 Web API 服务...")
    print("📍 服务地址: http://localhost:8000")
    print("📖 API文档: http://localhost:8000/docs")
    print("🔌 WebSocket: ws://localhost:8000/ws")
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
