#!/usr/bin/env python3
"""
综合测试：验证AI回答来源标注与上下文管理系统的完整集成
"""

import asyncio
import websockets
import json
import sys
import os
from datetime import datetime

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'web_turbine_interface/turbine_backend/app/services'))

async def test_websocket_integration():
    """测试WebSocket集成功能"""
    print("🧪 开始综合测试：AI回答来源标注与上下文管理")
    
    uri = "ws://localhost:8000/ws"
    
    try:
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接成功")
            
            # 测试1：发送第一个查询
            print("\n📤 测试1：发送第一个查询")
            query1 = {
                "type": "user_query",
                "message": "风机齿轮箱温度异常，如何诊断？",
                "timestamp": datetime.now().isoformat()
            }
            
            await websocket.send(json.dumps(query1))
            print(f"发送查询: {query1['message']}")
            
            # 接收响应
            response_count = 0
            while response_count < 10:  # 限制接收消息数量
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    data = json.loads(response)
                    print(f"📨 收到响应 {response_count + 1}: {data['type']}")
                    
                    if data['type'] == 'processing_start':
                        print(f"   消息: {data['message']}")
                        if 'context_info' in data:
                            print(f"   上下文信息: {data['context_info']}")
                    
                    elif data['type'] == 'tool_recommendation':
                        print(f"   推荐工具: {[tool['name'] for tool in data.get('tools', [])]}")
                        
                        # 选择第一个工具
                        if data.get('tools'):
                            tool_selection = {
                                "type": "tool_selection",
                                "selected_tools": [data['tools'][0]['name']],
                                "session_id": data.get('session_id', 'default'),
                                "timestamp": datetime.now().isoformat()
                            }
                            await websocket.send(json.dumps(tool_selection))
                            print(f"   选择工具: {data['tools'][0]['name']}")
                    
                    elif data['type'] == 'final_answer':
                        print(f"   最终回答: {data['message'][:100]}...")
                        if 'source_attribution' in data:
                            print(f"   来源标注: {data['source_attribution']}")
                        break
                    
                    response_count += 1
                    
                except asyncio.TimeoutError:
                    print("⏰ 接收超时，继续下一个测试")
                    break
            
            # 等待一下，让后端处理完成
            await asyncio.sleep(2)
            
            # 测试2：发送第二个相关查询（测试上下文）
            print("\n📤 测试2：发送相关查询（测试上下文）")
            query2 = {
                "type": "user_query", 
                "message": "齿轮箱维护周期是多久？",
                "timestamp": datetime.now().isoformat()
            }
            
            await websocket.send(json.dumps(query2))
            print(f"发送查询: {query2['message']}")
            
            # 接收第二个查询的响应
            response_count = 0
            while response_count < 10:
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    data = json.loads(response)
                    print(f"📨 收到响应 {response_count + 1}: {data['type']}")
                    
                    if data['type'] == 'processing_start':
                        print(f"   消息: {data['message']}")
                        if 'context_info' in data:
                            context = data['context_info']
                            print(f"   🧠 上下文摘要: {context.get('context_summary', 'N/A')}")
                            print(f"   📚 历史消息: {context.get('has_history', False)}")
                            print(f"   🔍 相关记忆数: {context.get('relevant_memories_count', 0)}")
                            if context.get('intelligent_insights'):
                                insights = context['intelligent_insights']
                                print(f"   💡 智能洞察: {insights}")
                    
                    elif data['type'] == 'tool_recommendation':
                        print(f"   推荐工具: {[tool['name'] for tool in data.get('tools', [])]}")
                        
                        # 选择第一个工具
                        if data.get('tools'):
                            tool_selection = {
                                "type": "tool_selection",
                                "selected_tools": [data['tools'][0]['name']],
                                "session_id": data.get('session_id', 'default'),
                                "timestamp": datetime.now().isoformat()
                            }
                            await websocket.send(json.dumps(tool_selection))
                            print(f"   选择工具: {data['tools'][0]['name']}")
                    
                    elif data['type'] == 'final_answer':
                        print(f"   最终回答: {data['message'][:100]}...")
                        if 'source_attribution' in data:
                            print(f"   来源标注: {data['source_attribution']}")
                        break
                    
                    response_count += 1
                    
                except asyncio.TimeoutError:
                    print("⏰ 接收超时，测试完成")
                    break
            
            print("\n🎉 综合测试完成！")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

async def test_session_manager_integration():
    """测试SessionManager集成"""
    print("\n🧪 测试SessionManager集成")
    
    from session_manager import SessionManager
    
    session_manager = SessionManager()
    
    # 创建测试会话
    session_id = "integration_test_001"
    session = await session_manager.create_session(session_id)
    print(f"✅ 创建测试会话: {session.session_id}")
    
    # 添加第一条消息
    await session_manager.add_message(
        session_id=session_id,
        user_query="风机齿轮箱温度异常，如何诊断？",
        ai_response="齿轮箱温度异常通常由以下原因引起：1. 润滑油不足或老化 2. 轴承磨损 3. 负载过大。建议检查润滑系统和轴承状态。",
        tool_results={"wind-turbine-db": {"success": True, "data": "温度数据"}},
        source_attribution={"tool_sources": ["wind-turbine-db"]}
    )
    print("✅ 添加第一条消息")
    
    # 添加第二条消息
    await session_manager.add_message(
        session_id=session_id,
        user_query="齿轮箱维护周期是多久？",
        ai_response="齿轮箱的维护周期通常为：1. 日常检查：每天 2. 润滑油更换：6个月 3. 大修：2-3年。具体周期需根据运行环境和负载情况调整。",
        tool_results={"local-knowledge-base": {"success": True, "data": "维护手册"}},
        source_attribution={"tool_sources": ["local-knowledge-base"]}
    )
    print("✅ 添加第二条消息")
    
    # 获取上下文
    context = await session_manager.get_conversation_context(
        session_id,
        current_query="如何判断齿轮箱是否需要大修？"
    )
    
    print(f"✅ 获取上下文成功:")
    print(f"   历史消息数: {len(context['recent_messages'])}")
    print(f"   上下文摘要: {context['context_summary']}")
    print(f"   关键实体: {context['key_entities']}")
    print(f"   相关记忆数: {len(context.get('relevant_memories', []))}")
    
    if context.get('intelligent_insights'):
        insights = context['intelligent_insights']
        print(f"   智能洞察: {insights}")
    
    print("🎉 SessionManager集成测试完成！")

async def main():
    """主测试函数"""
    print("🚀 开始综合系统测试")
    print("=" * 60)
    
    # 测试1：SessionManager集成
    await test_session_manager_integration()
    
    print("\n" + "=" * 60)
    
    # 测试2：WebSocket集成
    await test_websocket_integration()
    
    print("\n" + "=" * 60)
    print("🎉 所有测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
