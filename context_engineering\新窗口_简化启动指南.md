# 新窗口简化启动指南 🚀

## 📋 项目当前状态 (2025-07-30更新)
- **当前阶段**: C7-001 上下文记忆增强测试 ✅ 已完成
- **系统状态**: 前端/后端正常运行，上下文记忆功能完美
- **技术债务**: SourceAttribution组件渲染失效 (高优先级修复项)
- **下一步**: 修复组件问题后继续Phase C7开发

## 🎯 新人必读：按顺序看文档 (5分钟上手)

### 第1步：了解当前任务 (1分钟) ⭐ 必读
**文件**: `AD_HOC_TASKS.md`
**看什么**:
- 只看"🔥 高优先级"部分：SourceAttribution组件渲染失效
- ✅ 已验证解决：SessionManager和模块导入问题实际不存在
- **核心任务**: 修复Vue组件渲染问题，恢复信息来源标注功能

### 第2步：了解最新测试成果 (2分钟) ⭐ 必读
**文件**: `C7-001_上下文记忆增强测试_完成总结.md`
**看什么**:
- 测试结果：5轮对话测试100%成功
- 核心能力：记忆准确性100%，语义理解95%
- 发现问题：SourceAttribution组件失效需要修复
- **重要结论**: 上下文记忆功能已达到生产就绪标准

### 第3步：了解项目背景 (2分钟) 📚 建议
**文件**: `CONTEXT_QUICK.md` 或 `README.md`
**看什么**:
- 项目架构：FastAPI后端 + Vue.js前端 + LangGraph智能体
- 核心功能：智能工具推荐、上下文记忆、专业技术咨询
- 技术栈：WebSocket通信、Playwright测试、TDD开发

### 第4步：了解历史成果 (1分钟) 📖 可选
**文件**: `C5数学公式重复渲染修复_完成总结.md`
**看什么**:
- 已完成：C0-C5全部阶段，数学公式、表格渲染、工具推荐等
- 性能基准：5.3s总响应时间，智能路由器0.001s响应
- 技术成就：范式转移架构、结构化内容渲染

## ❌ 暂时不要看的文件 (避免信息过载)
- 历史完成总结：`C0-C5`相关完成总结文件 - 已完成的历史成果
- 详细报告：`CONTEXT_PERFORMANCE_ANALYSIS_REPORT.md` - 性能分析详细报告
- 历史任务：`新窗口_技术债务清理任务.md` - 已验证不存在的问题

## 🔧 立即验证系统状态

```bash
# 1. 启动后端 (应该正常启动)
cd web_turbine_interface/turbine_backend
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# 2. 启动前端 (应该正常启动)
cd web_turbine_interface/turbine_frontend
npm run dev

# 3. 验证服务
# 前端: http://localhost:3000 - 应该正常显示
# 后端: http://localhost:8000 - 应该正常响应
```

## 🎯 当前任务目标

- 🔴 **修复SourceAttribution组件渲染失效** (高优先级)
- ✅ 验证上下文记忆功能正常 (已完成)
- ✅ 确保前后端正常启动 (已验证)
- 🔄 **使用Playwright MCP进行TDD测试验证**

## 📍 服务地址

- **前端**: http://localhost:3000
- **后端**: http://localhost:8000

## 🚨 重要原则

1. **TDD测试驱动开发** - 使用Playwright MCP进行端到端验证
2. **保持已有成果** - 不破坏C0-C7已完成的功能成果
3. **组件修复优先** - 先修复SourceAttribution组件再开发新功能
4. **系统稳定性至上** - 确保前后端服务正常运行

## 🔍 关键技术信息

### 服务地址 (标准配置)
- **前端服务**: http://localhost:3000
- **后端服务**: http://localhost:8000
- **WebSocket**: ws://localhost:8000/ws

### 核心技术栈
- **后端**: FastAPI + WebSocket + LangGraph智能体
- **前端**: Vue.js 3 + Element Plus + KaTeX数学渲染
- **测试**: Playwright MCP浏览器自动化测试
- **架构**: 范式转移架构，后端结构化内容生成

### 已验证功能状态
- ✅ **上下文记忆**: 跨查询记忆100%准确，语义理解95%
- ✅ **智能工具推荐**: 6个工具智能推荐和手动选择
- ✅ **内容渲染**: 数学公式、表格、代码块完美渲染
- ✅ **WebSocket通信**: 实时双向通信稳定
- 🔴 **信息来源标注**: SourceAttribution组件失效，需要修复

---

**🎯 核心任务**: 修复SourceAttribution组件，恢复AI回答的信息来源标注功能！
