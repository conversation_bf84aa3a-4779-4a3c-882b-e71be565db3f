# 新窗口简化启动指南 🚀

## 🎯 新人必读：按顺序看文档 (5分钟上手)

### 第1步：了解当前任务 (1分钟) ⭐ 必读
**文件**: `新窗口_技术债务清理任务.md`
**看什么**: 
- 当前要做什么：修复SessionManager异步初始化问题
- 成功标准：后端能正常启动，无RuntimeError
- 具体方案：延迟初始化模式

### 第2步：了解具体问题 (1分钟) ⭐ 必读  
**文件**: `AD_HOC_TASKS.md`
**看什么**:
- 只看"🔥 高优先级"部分
- SessionManager异步初始化冲突
- 模块导入路径混乱问题
- **跳过其他部分**

### 第3步：了解项目背景 (2分钟) 📚 建议
**文件**: `CONTEXT_QUICK.md`
**看什么**:
- "当前状态"：C5完成，技术债务清理阶段
- "最近关键突破"：数学公式修复完成
- "重要提醒"：技术债务必须先解决

### 第4步：了解最新成果 (1分钟) 📖 可选
**文件**: `C5数学公式重复渲染修复_完成总结.md`
**看什么**:
- 刚完成的工作：数学公式重复渲染已修复
- 性能基准：5.3s总响应时间
- 发现的问题：技术债务需要清理

## ❌ 暂时不要看的文件 (避免信息过载)
- `C4AI回答来源标注与上下文管理任务_完成总结.md` - 历史成果
- `CONTEXT_PERFORMANCE_ANALYSIS_REPORT.md` - 详细报告
- `C2-002_范式转移架构实施_完成总结.md` - 历史成果
- 其他C0-C3相关文件 - 历史成果

## 🔧 立即验证当前问题

```bash
# 测试后端启动 (应该会报错)
cd web_turbine_interface/turbine_backend
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# 预期错误:
# RuntimeError: no running event loop
# 模块导入失败
```

## 🎯 当前任务目标

- ✅ 修复SessionManager异步初始化问题
- ✅ 统一模块导入路径配置  
- ✅ 确保后端正常启动
- ✅ 使用Playwright MCP验证修复效果

## 📍 服务地址

- **前端**: http://localhost:3000
- **后端**: http://localhost:8000

## 🚨 重要原则

1. **技术债务优先** - 先修复基础设施再开发新功能
2. **保持已有成果** - 不破坏C0-C5的完成成果  
3. **测试驱动** - 每个修复都要验证
4. **系统稳定性至上** - 以后端正常启动为准

---

**🎯 核心任务**: 让后端能正常启动，无异步初始化错误！
