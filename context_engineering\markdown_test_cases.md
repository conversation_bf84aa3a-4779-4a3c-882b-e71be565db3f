# Markdown渲染测试用例

## 测试查询1：表格格式测试

请生成一个包含以下表格的技术报告：

| 工况编号 | 风量 Q (m³/s) | 压力 P (Pa) | 效率 η (%) |
|---------|---------------|-------------|------------|
| 1       | 1500          | 800         | 85.2       |
| 2       | 2000          | 1200        | 87.5       |
| 3       | 2500          | 1600        | 89.1       |

## 测试查询2：代码块格式测试

请提供以下Python代码的分析：

```python
def calculate_power(wind_speed, air_density=1.225):
    """计算风机功率"""
    swept_area = 3.14159 * (rotor_radius ** 2)
    power = 0.5 * air_density * swept_area * (wind_speed ** 3)
    return power
```

## 测试查询3：数学公式测试

请解释风机功率计算公式：

$$P = \frac{1}{2} \rho A v^3 C_p$$

其中：
- $P$ 是功率 (W)
- $\rho$ 是空气密度 (kg/m³)
- $A$ 是扫掠面积 (m²)
- $v$ 是风速 (m/s)
- $C_p$ 是功率系数

## 测试查询4：综合测试

请生成一个包含表格、代码和公式的完整技术报告：

### 风机性能分析报告

#### 1. 测试数据

| 测试点 | 风速 (m/s) | 功率 (kW) | 转速 (rpm) |
|--------|------------|-----------|------------|
| A      | 5.0        | 125       | 15.2       |
| B      | 8.0        | 520       | 24.8       |
| C      | 12.0       | 1850      | 37.5       |

#### 2. 计算代码

```python
import numpy as np

def wind_power_curve(wind_speeds, rated_power=2000):
    """风机功率曲线计算"""
    powers = []
    for v in wind_speeds:
        if v < 3:  # 切入风速
            power = 0
        elif v < 12:  # 额定风速以下
            power = rated_power * (v/12)**3
        elif v < 25:  # 额定功率区间
            power = rated_power
        else:  # 切出风速以上
            power = 0
        powers.append(power)
    return np.array(powers)
```

#### 3. 理论公式

功率系数的计算公式为：

$$C_p = \frac{P}{\frac{1}{2} \rho A v^3}$$

最大理论效率（贝茨极限）：

$$C_{p,max} = \frac{16}{27} \approx 0.593$$

## 预期测试结果

1. **表格渲染**：应显示为带边框的HTML表格，表头有背景色
2. **代码块渲染**：应显示为带语法高亮的代码块，有背景色和边框
3. **数学公式渲染**：应使用KaTeX渲染为数学符号，行内公式和块级公式都正确显示
4. **综合效果**：所有元素在同一消息中正确渲染，格式美观专业

## 测试步骤

1. 启动系统后，依次输入上述测试查询
2. 观察AI回复中的Markdown元素渲染效果
3. 验证表格、代码块、数学公式是否正确显示
4. 检查是否还有格式异常问题

## 验收标准

- [ ] 表格显示为HTML表格格式，不是纯文本
- [ ] 代码块显示为`<pre><code>`格式，不是`code [ref=xxx]: text`
- [ ] 数学公式正确渲染，不是纯文本
- [ ] 所有Markdown元素在技术报告中专业美观
