"""
LangGraph WebSocket适配器 - 将同步LangGraph智能体适配到异步WebSocket通信
"""

import asyncio
import sys
import os
import logging
from typing import Dict, Any, List, Optional, AsyncGenerator
from datetime import datetime
import json

# 添加主项目路径 - 修复路径计算
# 当前文件: web_turbine_interface/turbine_backend/app/services/langgraph_websocket_adapter.py
# 需要到达: local_hybrid_agent/ (项目根目录)
main_project_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..'))
src_path = os.path.join(main_project_path, 'src')

sys.path.insert(0, main_project_path)
sys.path.insert(0, src_path)

# 导入友好错误处理器
from app.utils.error_handler import error_handler

# 导入智能路由器
from .intelligent_router import IntelligentRouter

# 导入会话管理器
from .session_manager import SessionManager

logger = logging.getLogger(__name__)

# 调试路径信息
logger.info(f"🔧 LangGraphWebSocketAdapter路径调试:")
logger.info(f"  当前文件: {__file__}")
logger.info(f"  项目根目录: {main_project_path}")
logger.info(f"  src目录: {src_path}")
logger.info(f"  src目录存在: {os.path.exists(src_path)}")

class LangGraphWebSocketAdapter:
    """LangGraph WebSocket适配器"""
    
    def __init__(self):
        """初始化适配器"""
        self.agent = None
        self.is_initialized = False
        self.initialization_error = None
        self.pending_user_choices = {}  # 存储待确认的用户选择
        self.router = IntelligentRouter()  # 初始化智能路由器
        self.session_manager = SessionManager()  # 初始化会话管理器
        
    async def initialize(self) -> bool:
        """初始化InteractiveLangGraphAgent"""
        if self.is_initialized:
            return True
            
        try:
            logger.info("🔧 初始化InteractiveLangGraphAgent...")

            # 导入InteractiveLangGraphAgent - 修复导入路径
            import sys
            import os

            # 添加项目根目录到Python路径
            project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
            src_path = os.path.join(project_root, 'src')
            if src_path not in sys.path:
                sys.path.insert(0, src_path)

            from interactive_langgraph_agent import InteractiveLangGraphAgent
            from config import get_config
            
            # 初始化配置和智能体
            config = get_config()
            self.agent = InteractiveLangGraphAgent(config)
            
            self.is_initialized = True
            logger.info("✅ InteractiveLangGraphAgent初始化完成")
            return True
            
        except Exception as e:
            error_msg = f"❌ InteractiveLangGraphAgent初始化失败: {str(e)}"
            logger.error(error_msg)
            self.initialization_error = error_msg
            return False
    
    async def process_query_stream(self, session_id: str, query: str) -> AsyncGenerator[Dict[str, Any], None]:
        """
        处理用户查询，返回流式WebSocket消息
        这是核心适配方法，将LangGraph的同步处理转换为WebSocket流式响应
        """
        
        # 确保智能体已初始化
        if not self.is_initialized:
            await self.initialize()
            
        if not self.is_initialized:
            # 使用友好错误处理
            friendly_error = error_handler.format_error_for_websocket(
                f"智能体未初始化: {self.initialization_error}",
                "agent_initialization"
            )
            yield friendly_error
            return
        
        try:
            # 1. 获取会话上下文（智能增强版）
            conversation_context = await self.session_manager.get_conversation_context(session_id, current_query=query)
            logger.info(f"📚 获取会话上下文: {session_id}, 历史消息数: {len(conversation_context['recent_messages'])}, 相关记忆数: {len(conversation_context.get('relevant_memories', []))}")

            # 2. 发送开始处理消息（包含智能洞察）
            context_info = {
                "has_history": len(conversation_context['recent_messages']) > 0,
                "context_summary": conversation_context['context_summary'],
                "relevant_memories_count": len(conversation_context.get('relevant_memories', [])),
                "intelligent_insights": conversation_context.get('intelligent_insights', {})
            }

            # 构建智能分析消息
            analysis_message = f"🤔 正在分析您的问题: {query}"
            if context_info["relevant_memories_count"] > 0:
                analysis_message += f" (发现 {context_info['relevant_memories_count']} 条相关历史经验)"

            yield {
                "type": "processing_start",
                "message": analysis_message,
                "query": query,
                "context_info": context_info,
                "timestamp": datetime.now().isoformat()
            }
            
            # 2. 模拟LangGraph的分析阶段
            await asyncio.sleep(0.5)
            yield {
                "type": "routing_analysis", 
                "message": "🧠 智能路由器正在分析查询类型...",
                "timestamp": datetime.now().isoformat()
            }
            
            # 3. 获取工具推荐和查询分析
            await asyncio.sleep(1)
            tool_recommendations = await self._get_tool_recommendations(query)

            # 获取查询分析信息
            intent = self.router.analyze_query_intent(query)

            yield {
                "type": "tool_recommendation",
                "message": "🔧 推荐使用以下工具进行分析:",
                "tools": tool_recommendations,
                "query_analysis": {
                    "query_type": intent.query_type,
                    "confidence": intent.confidence,
                    "reasoning": intent.reasoning
                },
                "session_id": session_id,
                "timestamp": datetime.now().isoformat()
            }
            
            # 4. 等待用户选择 - Human-in-the-Loop核心机制
            user_choice = await self._wait_for_user_choice(session_id, tool_recommendations)
            
            # 5. 根据用户选择执行工具
            selection_mode = user_choice.get("mode", "manual")
            selected_tools = user_choice.get("selected_tools", [])

            if selection_mode == "ai-only":
                # 仅AI分析模式 - 跳过工具执行
                yield {
                    "type": "ai_analysis",
                    "message": "🤖 使用AI直接分析，无需调用外部工具...",
                    "timestamp": datetime.now().isoformat()
                }
                tool_results = {}  # 空的工具结果

            elif selected_tools:
                # 有工具选择的模式
                mode_display = {
                    "required": "必需工具",
                    "all": "全部推荐工具",
                    "manual": "手动选择工具",
                    "timeout_default": "默认工具"
                }.get(selection_mode, "选定工具")

                yield {
                    "type": "data_retrieval",
                    "message": f"📊 正在使用{mode_display}检索数据: {', '.join(selected_tools)}",
                    "timestamp": datetime.now().isoformat()
                }

                # 使用新的并行执行方法
                tool_results = await self._execute_tools_parallel(selected_tools, query)

                await asyncio.sleep(1.5)

                # 6. AI分析阶段
                yield {
                    "type": "ai_analysis",
                    "message": "🤖 AI正在分析数据并生成回答...",
                    "timestamp": datetime.now().isoformat()
                }

            else:
                # 没有选择工具且不是AI-only模式
                yield {
                    "type": "error",
                    "message": "❌ 未选择任何工具且非AI分析模式",
                    "timestamp": datetime.now().isoformat()
                }
                return

            await asyncio.sleep(1)

            # 7. 生成最终回答（包含会话上下文）
            final_answer = await self._generate_final_answer(query, tool_results, user_choice, conversation_context)
            
            # 8. 发送流式最终回答
            async for chunk in self._stream_final_answer(final_answer, query):
                yield chunk

            # 9. 保存消息到会话历史
            try:
                # 提取来源标注信息（如果是结构化响应）
                source_attribution = None
                if isinstance(final_answer, dict) and final_answer.get("type") == "structured_response":
                    # 从结构化内容中提取来源标注
                    content_elements = final_answer.get("content", [])
                    source_attribution = {
                        "total_elements": len(content_elements),
                        "attributed_elements": sum(1 for elem in content_elements if elem.get("source_attribution")),
                        "tool_sources": list(set(
                            elem.get("source_attribution", {}).get("primary_source", {}).get("tool_name", "unknown")
                            for elem in content_elements
                            if elem.get("source_attribution")
                        ))
                    }

                await self.session_manager.add_message(
                    session_id=session_id,
                    user_query=query,
                    ai_response=final_answer,
                    tool_results=tool_results,
                    source_attribution=source_attribution
                )
                logger.info(f"✅ 消息已保存到会话历史: {session_id}")
            except Exception as e:
                logger.error(f"❌ 保存会话历史失败: {e}")

            # 10. 发送完成消息
            yield {
                "type": "processing_complete",
                "message": "✅ 查询处理完成",
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            error_msg = f"查询处理失败: {str(e)}"
            logger.error(f"❌ {error_msg}")

            # 使用友好错误处理
            friendly_error = error_handler.format_error_for_websocket(
                error_msg,
                "service_unavailable"
            )
            yield friendly_error
    
    async def _get_tool_recommendations(self, query: str) -> List[Dict[str, Any]]:
        """使用智能路由器获取工具推荐"""
        try:
            # 使用智能路由器分析查询并获取推荐
            recommendations = self.router.get_tool_recommendations(query)

            # 转换为WebSocket消息格式
            suggestions = []
            for rec in recommendations:
                suggestions.append({
                    "name": rec.name,
                    "description": rec.description,
                    "confidence": rec.confidence,
                    "estimated_time": rec.estimated_time,
                    "reason": rec.reason,
                    "category": rec.category
                })

            # 获取查询分析信息用于调试
            intent = self.router.analyze_query_intent(query)
            logger.info(f"智能路由器分析结果: 类型={intent.query_type}, 置信度={intent.confidence:.2f}")
            logger.info(f"推荐工具: {[s['name'] for s in suggestions]}")

            return suggestions

        except Exception as e:
            logger.error(f"智能路由器推荐失败: {e}")
            # 降级到静态推荐
            return [
                {
                    "name": "wind-turbine-db",
                    "description": "风机数据库查询",
                    "confidence": 0.7,
                    "estimated_time": 3,
                    "reason": "默认推荐 (智能路由器不可用)",
                    "category": "required"
                }
            ]
    
    async def _wait_for_user_choice(self, session_id: str, recommendations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """等待用户选择 - Human-in-the-Loop核心实现"""

        # 存储推荐供用户选择
        self.pending_user_choices[session_id] = {
            "recommendations": recommendations,
            "status": "waiting",
            "timestamp": datetime.now().isoformat(),
            "user_choice": None
        }

        # 等待用户通过WebSocket发送选择
        max_wait_time = 300  # 最多等待5分钟
        check_interval = 0.5  # 每0.5秒检查一次
        waited_time = 0

        while waited_time < max_wait_time:
            # 检查是否收到用户选择
            if session_id in self.pending_user_choices:
                choice_data = self.pending_user_choices[session_id]
                if choice_data.get("status") == "completed" and choice_data.get("user_choice"):
                    user_choice = choice_data["user_choice"]
                    # 清理已完成的选择
                    del self.pending_user_choices[session_id]

                    return {
                        "selected_tools": user_choice.get("selected_tools", []),
                        "mode": user_choice.get("mode", "manual"),  # 新增：包含选择模式
                        "choice_type": "user_select",
                        "timestamp": datetime.now().isoformat()
                    }

            await asyncio.sleep(check_interval)
            waited_time += check_interval

        # 超时处理：使用第一个推荐工具作为默认选择
        logger.warning(f"用户选择超时 (session: {session_id})，使用默认选择")
        if session_id in self.pending_user_choices:
            del self.pending_user_choices[session_id]

        selected_tools = [recommendations[0]["name"]] if recommendations else []

        return {
            "selected_tools": selected_tools,
            "mode": "timeout_default",  # 超时默认模式
            "choice_type": "timeout_default",
            "timestamp": datetime.now().isoformat()
        }
    
    async def _execute_selected_tools(self, selected_tools: List[str], query: str) -> Dict[str, Any]:
        """执行选定的工具 - 调用真实的InteractiveLangGraphAgent"""
        results = {}

        try:
            # 创建状态对象来调用真实的工具执行逻辑
            from interactive_langgraph_agent import InteractiveAgentState

            # 构建状态
            state = {
                "user_query": query,
                "selected_tools": selected_tools,
                "local_data": {},
                "tech_docs": {},
                "online_data": {},
                "error": None
            }

            # 调用真实的工具执行节点
            updated_state = await self.agent._execute_tools_node(state)

            # 转换结果格式
            for tool_name in selected_tools:
                if tool_name == "local-knowledge-base" and updated_state.get("tech_docs"):
                    results[tool_name] = {
                        "success": True,
                        "data": updated_state["tech_docs"],
                        "source": "本地知识库"
                    }
                elif tool_name == "wind-turbine-db" and updated_state.get("local_data"):
                    results[tool_name] = {
                        "success": True,
                        "data": updated_state["local_data"],
                        "source": "风机数据库"
                    }
                elif tool_name == "context7" and updated_state.get("tech_docs"):
                    results[tool_name] = {
                        "success": True,
                        "data": updated_state["tech_docs"],
                        "source": "技术文档库"
                    }
                elif tool_name == "fetch" and updated_state.get("online_data"):
                    results[tool_name] = {
                        "success": True,
                        "data": updated_state["online_data"],
                        "source": "在线搜索"
                    }
                elif tool_name == "pdf-processor" and updated_state.get("file_data"):
                    results[tool_name] = {
                        "success": True,
                        "data": updated_state["file_data"],
                        "source": "PDF处理器"
                    }
                elif tool_name == "filesystem" and updated_state.get("file_data"):
                    results[tool_name] = {
                        "success": True,
                        "data": updated_state["file_data"],
                        "source": "文件系统"
                    }
                else:
                    # 如果没有数据，提供默认结果
                    results[tool_name] = {
                        "success": True,
                        "data": f"工具 {tool_name} 执行完成，但未返回具体数据",
                        "source": tool_name
                    }

        except Exception as e:
            logger.error(f"工具执行失败: {e}")
            # 降级到模拟结果，使用友好错误信息
            for tool_name in selected_tools:
                friendly_error = error_handler.get_user_friendly_error(
                    f"工具执行失败: {str(e)}",
                    "tool_execution"
                )
                results[tool_name] = {
                    "success": False,
                    "error": friendly_error["user_message"],
                    "error_details": friendly_error
                }

        return results
    
    async def _generate_final_answer(self, query: str, tool_results: Dict[str, Any],
                                   user_choice: Dict[str, Any], conversation_context: Dict[str, Any]) -> str:
        """生成最终回答 - 调用真实的AI分析（包含会话上下文）"""
        try:
            # 构建状态对象来调用真实的AI处理逻辑
            state = {
                "user_query": query,
                "selected_tools": user_choice.get("selected_tools", []),
                "local_data": {},
                "tech_docs": {},
                "online_data": {},
                "file_data": {},
                "final_response": "",
                "error": None,
                "stream_mode": False,  # WebSocket环境下不使用流式模式
                "query_analysis": {},  # 添加查询分析字段
                "conversation_context": conversation_context  # 🚀 新增：会话上下文
            }

            # 填充工具结果到状态中
            for tool_name, result in tool_results.items():
                if result.get("success"):
                    if tool_name == "wind-turbine-db":
                        state["local_data"] = result["data"]
                    elif tool_name in ["local-knowledge-base", "context7"]:
                        state["tech_docs"] = result["data"]
                    elif tool_name == "fetch":
                        state["online_data"] = result["data"]
                    elif tool_name in ["pdf-processor", "filesystem"]:
                        state["file_data"] = result["data"]

            logger.info(f"🤖 调用真实AI分析，查询: {query}")
            logger.info(f"📊 工具数据: {list(tool_results.keys())}")

            # 调用真实的AI处理节点
            updated_state = await self.agent._process_with_ai_node(state)

            # 如果AI处理成功，返回AI生成的回答
            if updated_state.get("final_response"):
                logger.info("✅ AI分析成功，返回真实回答")

                # 🚀 范式转移：处理结构化响应
                final_response = updated_state["final_response"]
                if isinstance(final_response, dict) and final_response.get("type") == "structured_response":
                    logger.info("📊 检测到结构化响应，直接返回")
                    return final_response
                else:
                    logger.info("📝 检测到传统Markdown响应，保持兼容")
                    return final_response
            else:
                logger.warning("⚠️ AI分析未返回回答，使用降级方案")
                # 降级到基础格式化回答
                return self._format_basic_answer(query, tool_results)

        except Exception as e:
            logger.error(f"❌ AI回答生成失败: {e}")
            logger.error(f"错误详情: {str(e)}")

            # 使用友好错误处理生成降级回答
            friendly_error = error_handler.get_user_friendly_error(
                f"AI回答生成失败: {str(e)}",
                "ai_analysis"
            )

            return f"""## {friendly_error['user_message']}

{friendly_error['description']}

### 💡 建议解决方案：
{chr(10).join(f"• {suggestion}" for suggestion in friendly_error['suggestions'])}

---
*如果问题持续，请联系技术支持团队*"""

    def _format_basic_answer(self, query: str, tool_results: Dict[str, Any]) -> str:
        """格式化基础回答（降级方案）- 提供更专业的格式"""
        answer_parts = [f"## 关于「{query}」的分析报告\n"]

        if tool_results:
            answer_parts.append("### 📊 数据查询结果")
            for tool_name, result in tool_results.items():
                if result.get("success"):
                    source_name = result.get("source", tool_name)
                    data = result.get("data", {})

                    # 格式化风机数据库结果
                    if tool_name == "wind-turbine-db" and isinstance(data, dict):
                        if data.get("results"):
                            answer_parts.append(f"\n**{source_name}查询结果：**")
                            for item in data["results"][:3]:  # 只显示前3个结果
                                if isinstance(item, dict):
                                    name = item.get("name", item.get("fault_name", "未知"))
                                    desc = item.get("description", "无描述")
                                    severity = item.get("severity", "未知")
                                    answer_parts.append(f"- **{name}** (严重程度: {severity})")
                                    answer_parts.append(f"  {desc}")
                        else:
                            answer_parts.append(f"\n**{source_name}：** 未找到相关数据")
                    else:
                        # 其他工具的简化显示
                        answer_parts.append(f"\n**{source_name}：** 数据已获取")
                else:
                    answer_parts.append(f"- {tool_name}: 查询失败 - {result.get('error', '未知错误')}")

        answer_parts.append(f"\n### 🤖 AI专业分析")
        answer_parts.append("抱歉，AI分析服务暂时不可用。基于查询到的数据，建议您：")
        answer_parts.append("1. 检查相关设备的运行状态")
        answer_parts.append("2. 参考技术手册进行故障排查")
        answer_parts.append("3. 如需详细分析，请稍后重试或联系技术支持")

        return "\n".join(answer_parts)
    
    async def _generate_ai_only_answer(self, query: str) -> str:
        """生成仅基于AI的回答 - 调用真实AI分析"""
        try:
            # 构建状态对象来调用真实的AI处理逻辑
            state = {
                "user_query": query,
                "selected_tools": [],
                "local_data": {},
                "tech_docs": {},
                "online_data": {},
                "file_data": {},
                "final_response": "",
                "error": None,
                "stream_mode": False,  # WebSocket环境下不使用流式模式
                "query_analysis": {}  # 添加查询分析字段
            }

            logger.info(f"🤖 调用真实AI分析（仅AI模式），查询: {query}")

            # 调用真实的AI处理节点
            updated_state = await self.agent._process_with_ai_node(state)

            # 如果AI处理成功，返回AI生成的回答
            if updated_state.get("final_response"):
                logger.info("✅ AI分析成功，返回真实回答")

                # 🚀 范式转移：处理结构化响应
                final_response = updated_state["final_response"]
                if isinstance(final_response, dict) and final_response.get("type") == "structured_response":
                    logger.info("📊 检测到结构化响应，直接返回")
                    return final_response
                else:
                    logger.info("📝 检测到传统Markdown响应，保持兼容")
                    return final_response
            else:
                logger.warning("⚠️ AI分析未返回回答，使用降级方案")
                return f"基于AI专业分析，针对您的问题「{query}」，我提供以下专业建议：\n\n由于当前AI服务暂时不可用，建议您稍后重试或联系技术支持。"

        except Exception as e:
            logger.error(f"❌ AI回答生成失败: {e}")
            return f"抱歉，AI分析过程中遇到技术问题。针对您的问题「{query}」，建议您稍后重试或联系技术支持。\n\n错误信息：{str(e)}"
    
    def handle_user_choice(self, session_id: str, choice_data: Dict[str, Any]) -> bool:
        """处理用户选择 - 供WebSocket端点调用"""
        if session_id in self.pending_user_choices:
            self.pending_user_choices[session_id].update({
                "user_choice": choice_data,
                "status": "completed",
                "completed_at": datetime.now().isoformat()
            })
            return True
        return False
    
    def cleanup_pending_choices(self, session_id: str) -> None:
        """清理指定会话的待处理用户选择"""
        if session_id in self.pending_user_choices:
            logger.info(f"🧹 清理会话 {session_id} 的待处理用户选择")
            del self.pending_user_choices[session_id]

    # 🚀 新增：会话管理API方法
    async def get_session_context(self, session_id: str) -> Dict[str, Any]:
        """获取会话上下文信息"""
        return await self.session_manager.get_conversation_context(session_id)

    async def update_session_preferences(self, session_id: str, preferences: Dict[str, Any]) -> bool:
        """更新会话用户偏好"""
        try:
            await self.session_manager.update_user_preferences(session_id, preferences)
            return True
        except Exception as e:
            logger.error(f"❌ 更新会话偏好失败: {e}")
            return False

    async def archive_session(self, session_id: str) -> bool:
        """归档会话"""
        return await self.session_manager.archive_session(session_id)

    async def delete_session(self, session_id: str) -> bool:
        """删除会话"""
        # 同时清理待处理的用户选择
        self.cleanup_pending_choices(session_id)
        return await self.session_manager.delete_session(session_id)

    def get_session_stats(self) -> Dict[str, Any]:
        """获取会话统计信息"""
        return self.session_manager.get_session_stats()

    def _validate_tool_combination(self, selected_tools: List[str]) -> bool:
        """验证工具组合的有效性"""
        # 定义可用的工具列表 - 扩展到6个工具
        available_tools = {
            "local-knowledge-base": "本地知识库搜索",
            "wind-turbine-db": "风机数据库查询",
            "context7": "技术文档搜索",
            "fetch": "在线信息获取",
            "pdf-processor": "PDF文档处理",
            "filesystem": "文件系统操作"
        }

        # 检查所有选择的工具是否都在可用工具列表中
        for tool in selected_tools:
            if tool not in available_tools:
                logger.warning(f"❌ 无效工具: {tool}")
                return False

        logger.info(f"✅ 工具组合验证通过: {selected_tools}")
        return True

    async def _execute_tools_parallel(self, selected_tools: List[str], query: str) -> Dict[str, Any]:
        """并行执行多个工具"""
        logger.info(f"🔧 开始并行执行 {len(selected_tools)} 个工具")

        # 验证工具组合
        if not self._validate_tool_combination(selected_tools):
            return {
                "error": "无效的工具组合",
                "selected_tools": selected_tools
            }

        # 创建并行任务
        tasks = []
        for tool_name in selected_tools:
            task = asyncio.create_task(
                self._execute_single_tool(tool_name, query),
                name=f"execute_{tool_name}"
            )
            tasks.append(task)

        # 等待所有任务完成
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 整理结果
            tool_results = {}
            for i, result in enumerate(results):
                tool_name = selected_tools[i]

                if isinstance(result, Exception):
                    logger.error(f"❌ 工具 {tool_name} 执行失败: {result}")
                    tool_results[tool_name] = {
                        "tool_name": tool_name,
                        "success": False,
                        "error": str(result),
                        "execution_time": 0
                    }
                else:
                    logger.info(f"✅ 工具 {tool_name} 执行成功")
                    tool_results[tool_name] = result

            return tool_results

        except Exception as e:
            logger.error(f"❌ 并行工具执行失败: {e}")
            return {
                "error": f"并行执行失败: {str(e)}",
                "selected_tools": selected_tools
            }

    async def _execute_single_tool(self, tool_name: str, query: str) -> Dict[str, Any]:
        """执行单个工具"""
        import time
        start_time = time.time()

        try:
            logger.info(f"🔧 执行工具: {tool_name}")

            # 根据工具类型调用相应的执行逻辑
            if tool_name == "local-knowledge-base":
                result = await self._call_local_knowledge_base(query)
            elif tool_name == "wind-turbine-db":
                result = await self._call_wind_turbine_db(query)
            elif tool_name == "context7":
                result = await self._call_context7(query)
            elif tool_name == "fetch":
                result = await self._call_fetch(query)
            elif tool_name == "pdf-processor":
                result = await self._call_pdf_processor(query)
            elif tool_name == "filesystem":
                result = await self._call_filesystem(query)
            else:
                raise ValueError(f"未知工具: {tool_name}")

            execution_time = time.time() - start_time

            return {
                "tool_name": tool_name,
                "success": True,
                "data": result,
                "execution_time": execution_time
            }

        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"❌ 工具 {tool_name} 执行失败: {e}")

            return {
                "tool_name": tool_name,
                "success": False,
                "error": str(e),
                "execution_time": execution_time
            }

    async def _call_local_knowledge_base(self, query: str) -> Dict[str, Any]:
        """调用本地知识库搜索工具"""
        try:
            # 调用真实的本地知识库搜索
            if self.agent and hasattr(self.agent, 'mcp_tools'):
                kb_result = self.agent.mcp_tools.call_mcp_tool("local-knowledge-base", "search_local_knowledge", {
                    "query": query,
                    "max_results": 5
                })

                if kb_result and kb_result.get("success"):
                    # 格式化本地知识库结果
                    results = kb_result.get("results", [])
                    if results:
                        # 合并所有相关内容
                        combined_content = []
                        for result in results[:3]:  # 取前3个最相关的结果
                            source = result.get("source_document", "未知来源")
                            content = result.get("content", "")
                            score = result.get("relevance_score", 0)
                            combined_content.append(f"[来源: {source}, 相关性: {score:.2f}]\n{content}")

                        final_content = "\n\n---\n\n".join(combined_content)
                        return {
                            "source": "local-knowledge-base",
                            "query": query,
                            "result": final_content,
                            "total_found": kb_result.get("total_found", 0),
                            "search_time": kb_result.get("search_time_seconds", 0),
                            "timestamp": datetime.now().isoformat()
                        }
                    else:
                        return {
                            "source": "local-knowledge-base",
                            "query": query,
                            "result": "未找到相关的本地文档内容",
                            "timestamp": datetime.now().isoformat()
                        }
                else:
                    error_msg = kb_result.get("error", "本地知识库查询失败") if kb_result else "本地知识库无响应"
                    return {
                        "source": "local-knowledge-base",
                        "query": query,
                        "result": f"查询失败: {error_msg}",
                        "timestamp": datetime.now().isoformat()
                    }
            else:
                # 降级到模拟结果
                await asyncio.sleep(0.1)  # 模拟本地搜索时间
                return {
                    "source": "local-knowledge-base",
                    "query": query,
                    "result": "本地知识库搜索结果（模拟 - MCP工具未初始化）",
                    "timestamp": datetime.now().isoformat()
                }
        except Exception as e:
            logger.error(f"本地知识库查询异常: {e}")
            return {
                "source": "local-knowledge-base",
                "query": query,
                "result": f"查询异常: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }

    async def _call_wind_turbine_db(self, query: str) -> Dict[str, Any]:
        """调用风机数据库查询工具"""
        # 这里调用实际的数据库查询逻辑
        # 暂时返回模拟数据
        await asyncio.sleep(0.5)  # 模拟数据库查询时间
        return {
            "source": "wind-turbine-db",
            "query": query,
            "result": "数据库查询结果（模拟）",
            "timestamp": datetime.now().isoformat()
        }

    async def _call_context7(self, query: str) -> Dict[str, Any]:
        """调用技术文档搜索工具"""
        # 这里调用实际的文档搜索逻辑
        await asyncio.sleep(0.3)  # 模拟文档搜索时间
        return {
            "source": "context7",
            "query": query,
            "result": "技术文档搜索结果（模拟）",
            "timestamp": datetime.now().isoformat()
        }

    async def _call_fetch(self, query: str) -> Dict[str, Any]:
        """调用在线信息获取工具"""
        # 这里调用实际的在线搜索逻辑
        await asyncio.sleep(0.8)  # 模拟在线搜索时间
        return {
            "source": "fetch",
            "query": query,
            "result": "在线信息获取结果（模拟）",
            "timestamp": datetime.now().isoformat()
        }

    async def _call_pdf_processor(self, query: str) -> Dict[str, Any]:
        """调用PDF文档处理工具"""
        try:
            # 调用真实的PDF处理工具
            if self.agent and hasattr(self.agent, 'mcp_tools'):
                # PDF处理器通常用于处理特定的PDF文件
                # 这里可以根据查询内容决定处理方式
                pdf_result = self.agent.mcp_tools.call_mcp_tool("pdf-processor", "process_pdf", {
                    "query": query,
                    "action": "search"  # 或者其他PDF处理动作
                })

                if pdf_result and pdf_result.get("success"):
                    return {
                        "source": "pdf-processor",
                        "query": query,
                        "result": pdf_result.get("result", "PDF处理完成"),
                        "timestamp": datetime.now().isoformat()
                    }
                else:
                    error_msg = pdf_result.get("error", "PDF处理失败") if pdf_result else "PDF处理器无响应"
                    return {
                        "source": "pdf-processor",
                        "query": query,
                        "result": f"处理失败: {error_msg}",
                        "timestamp": datetime.now().isoformat()
                    }
            else:
                # 降级到模拟结果
                await asyncio.sleep(1.5)  # 模拟PDF处理时间
                return {
                    "source": "pdf-processor",
                    "query": query,
                    "result": "PDF文档处理结果（模拟 - MCP工具未初始化）",
                    "timestamp": datetime.now().isoformat()
                }
        except Exception as e:
            logger.error(f"PDF处理异常: {e}")
            return {
                "source": "pdf-processor",
                "query": query,
                "result": f"处理异常: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }

    async def _call_filesystem(self, query: str) -> Dict[str, Any]:
        """调用文件系统操作工具"""
        try:
            # 调用真实的文件系统工具
            if self.agent and hasattr(self.agent, 'mcp_tools'):
                # 文件系统工具通常用于读取文件或列出目录
                # 根据查询内容决定操作类型
                if "读取" in query or "查看" in query:
                    action = "read_file"
                elif "列表" in query or "目录" in query:
                    action = "list_directory"
                else:
                    action = "search_files"

                fs_result = self.agent.mcp_tools.call_mcp_tool("filesystem", action, {
                    "query": query,
                    "path": "."  # 默认当前目录
                })

                if fs_result and fs_result.get("success"):
                    return {
                        "source": "filesystem",
                        "query": query,
                        "result": fs_result.get("result", "文件系统操作完成"),
                        "timestamp": datetime.now().isoformat()
                    }
                else:
                    error_msg = fs_result.get("error", "文件系统操作失败") if fs_result else "文件系统工具无响应"
                    return {
                        "source": "filesystem",
                        "query": query,
                        "result": f"操作失败: {error_msg}",
                        "timestamp": datetime.now().isoformat()
                    }
            else:
                # 降级到模拟结果
                await asyncio.sleep(0.3)  # 模拟文件系统操作时间
                return {
                    "source": "filesystem",
                    "query": query,
                    "result": "文件系统操作结果（模拟 - MCP工具未初始化）",
                    "timestamp": datetime.now().isoformat()
                }
        except Exception as e:
            logger.error(f"文件系统操作异常: {e}")
            return {
                "source": "filesystem",
                "query": query,
                "result": f"操作异常: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }

    async def _stream_final_answer(self, final_answer, query: str):
        """流式发送最终回答 - 🚀 范式转移：支持结构化响应"""
        import re

        # 首先发送开始流式回答的信号
        yield {
            "type": "streaming_start",
            "message": "🤖 正在生成回答...",
            "query": query,
            "timestamp": datetime.now().isoformat()
        }

        # 🚀 范式转移：检查是否为结构化响应
        if isinstance(final_answer, dict) and final_answer.get("type") == "structured_response":
            logger.info("📊 流式发送结构化响应")

            # 直接发送结构化响应，不进行分割
            yield {
                "type": "streaming_chunk",
                "message": final_answer,  # 发送完整的结构化响应
                "is_complete": True,
                "query": query,
                "timestamp": datetime.now().isoformat()
            }

            # 发送完成信号
            yield {
                "type": "streaming_complete",
                "message": final_answer,
                "query": query,
                "timestamp": datetime.now().isoformat()
            }

        else:
            # 传统字符串响应的处理逻辑
            logger.info("📝 流式发送传统Markdown响应")

            # 确保final_answer是字符串
            if not isinstance(final_answer, str):
                final_answer = str(final_answer)

            # 将回答按句子分割，保持语义完整性
            sentences = re.split(r'([。！？\n])', final_answer)
            current_text = ""

            for i, part in enumerate(sentences):
                if not part.strip():
                    continue

                current_text += part

                # 每个句子或换行符后发送一次更新
                if part in ['。', '！', '？', '\n'] or i == len(sentences) - 1:
                    yield {
                        "type": "streaming_chunk",
                        "message": current_text,
                        "is_complete": i == len(sentences) - 1,
                        "query": query,
                        "timestamp": datetime.now().isoformat()
                    }

                    # 添加适当的延迟，模拟打字效果
                    if i < len(sentences) - 1:
                        await asyncio.sleep(0.3)  # 句子间延迟300ms

            # 发送完成信号
            yield {
                "type": "streaming_complete",
                "message": current_text,
                "query": query,
                "timestamp": datetime.now().isoformat()
            }

    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            "initialized": self.is_initialized,
            "agent_available": self.agent is not None,
            "initialization_error": self.initialization_error,
            "pending_choices": len(self.pending_user_choices),
            "timestamp": datetime.now().isoformat()
        }
