# 新窗口_C4AI回答来源标注与上下文管理任务 🎯

## 📋 任务概览

**任务代号**: C4 - AI回答来源标注与上下文管理
**创建时间**: 2025-07-29 23:20
**预计完成**: 4-5小时
**优先级**: 🔥 HIGH - 核心用户体验功能

## 🎯 核心目标

基于前期成功完成的手动工具选择功能和表格渲染修复，现在实现两个关键的用户体验功能：
1. **AI回答工具来源标注** - 提高信息可信度和透明度
2. **会话上下文管理** - 实现真正的对话连续性

## 🔍 问题分析

### 问题1: AI回答工具来源标注缺失
**用户反馈**: "### 4. AI回答工具来源标注缺失 - **问题**: 缺乏信息来源标注，影响可信度"

**当前状态**:
- AI回答没有标明信息来源于哪个工具
- 用户无法追溯信息来源，影响专业性和可信度
- 缺少"展开数据来源"功能

### 问题2: 会话上下文管理完全缺失
**发现过程**:
- 测试查询："叶片断裂可能是什么问题" → AI详细回答
- 后续查询："那你提供下" → AI完全不知道指什么
- 终端日志显示每次查询都是独立处理

**根本原因**: 系统没有实现真正的会话上下文管理

## 🏗️ 技术方案

### Phase 1: AI回答工具来源标注 (优先级: HIGH)

#### Task 1.1: 后端数据结构增强 ⏱️ 30分钟
**目标**: 修改内容解析器，为每个内容元素添加来源标注
**文件**: `web_turbine_interface/turbine_backend/app/services/content_structure_parser.py`
**实现**:
```python
# 数据结构增强
interface SourceAttribution {
  toolName: string;
  toolIcon: string;
  confidence: number;
  contentRange: [number, number];
  originalData: any;
}
```

#### Task 1.2: 前端来源标注UI组件 ⏱️ 45分钟
**目标**: 创建来源标注组件，支持多种标注样式
**文件**: 新建 `web_turbine_interface/turbine_frontend/src/components/SourceAttribution.vue`
**功能**:
- 内联引用标记（如[📊][🔧][📚]）
- 段落级来源标签
- 悬浮提示显示详细来源信息
- 集成到StructuredMessageRenderer.vue

#### Task 1.3: 来源信息展示面板 ⏱️ 30分钟
**目标**: 实现"展开数据来源"功能
**功能**:
- 显示每个工具的具体贡献内容
- 提供原始数据查看入口
- 工具贡献度可视化

### Phase 2: 会话上下文管理 (优先级: HIGH)

#### Task 2.1: 会话状态管理器 ⏱️ 40分钟
**目标**: 在WebSocket适配器中实现会话上下文管理
**文件**: `web_turbine_interface/turbine_backend/app/services/langgraph_websocket_adapter.py`
**实现**:
```python
class SessionContextManager:
    def __init__(self):
        self.sessions = {}  # session_id -> context
    
    def get_context(self, session_id: str) -> List[Dict]:
        return self.sessions.get(session_id, {}).get('history', [])
    
    def add_interaction(self, session_id: str, query: str, response: str):
        # 添加到会话历史并进行智能压缩
        pass
```

#### Task 2.2: 智能上下文提取 ⏱️ 35分钟
**目标**: 集成现有的IntelligentMemoryManager
**文件**: 集成 `src/memory_enhancement/intelligent_memory_manager.py`
**功能**:
- 对话历史的语义分析和关键信息提取
- 在AI分析时自动注入相关上下文
- 实现上下文窗口管理（保留最近N轮对话）

#### Task 2.3: 前端会话持久化 ⏱️ 25分钟
**目标**: 增强前端会话管理功能
**文件**: `web_turbine_interface/turbine_frontend/src/App.vue`
**功能**:
- 实现会话间的上下文连续性
- 添加"继续上次对话"功能
- 会话历史可视化

### Phase 3: 系统集成与优化 (优先级: MEDIUM)

#### Task 3.1: 性能优化 ⏱️ 20分钟
- 实现上下文压缩算法
- 优化记忆存储和检索性能
- 添加缓存机制

#### Task 3.2: 用户体验增强 ⏱️ 25分钟
- 添加上下文可视化（显示AI"记住"了什么）
- 实现手动上下文管理（用户可编辑/删除记忆）
- 添加上下文重置功能

## 🧪 测试验证方案

### 服务地址
- **前端服务**: http://localhost:3000
- **后端服务**: http://localhost:8000

### 测试用例设计

#### 来源标注测试
```
测试1: 多工具查询
输入: "风机叶片断裂的原因分析和预防措施"
预期: AI回答中每部分信息都有明确的工具来源标注

测试2: 来源交互测试
操作: 点击来源标记
预期: 显示原始数据详情和工具贡献信息
```

#### 上下文管理测试
```
测试3: 上下文连续性
查询1: "叶片断裂可能是什么问题"
查询2: "那你提供下"
预期: AI能理解"提供下"指的是前面提到的模板

测试4: 跨会话记忆
操作: 新建会话，提及之前讨论的内容
预期: AI能够回忆相关信息
```

### TDD实验流程
1. **使用Playwright MCP进行自动化测试**
2. **Context7 MCP研究最佳实践**
3. **以前端服务测试效果为准**
4. **不生成冗余报告，专注实际效果**

## 📋 前置条件检查

### 必须解决的AD_HOC_TASKS问题
在开始本任务前，确保以下问题已解决：
- ✅ 手动工具选择功能 (已完成)
- ✅ 渐进式渲染Vue.js错误 (已完成)
- ✅ 表格渲染问题 (已完成)
- ⚠️ WebSocket ASGI错误 (需要检查)

### 系统状态验证
```bash
# 1. 环境激活
cd /e/风机智能体测试/local_hybrid_agent
conda activate viz

# 2. 启动服务
# 后端: cd web_turbine_interface/turbine_backend && python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
# 前端: cd web_turbine_interface/turbine_frontend && npm run dev

# 3. 基础功能验证
# 访问 http://localhost:3000
# 测试手动工具选择功能是否正常
```

## 🎯 成功指标

### 来源标注成功指标
- ✅ 用户可以清楚看到每部分信息来自哪个工具
- ✅ 点击来源标记可查看原始数据
- ✅ 提高AI回答的可信度和透明度

### 上下文管理成功指标
- ✅ 解决"那你提供下"类似的上下文缺失问题
- ✅ AI能够理解用户的指代和延续性需求
- ✅ 跨会话保持用户偏好和重要信息

## 🚨 重要提醒

### 开发原则
1. **TDD驱动**: 先写测试，再实现功能
2. **渐进式开发**: 每个Phase完成后立即测试验证
3. **用户体验优先**: 以实际使用效果为准
4. **不破坏现有功能**: 所有新功能都是增强

### 技术约束
- 保持现有的异步架构不变
- 利用已有的记忆管理组件
- 遵循范式转移架构的设计原则

## 📝 执行检查清单

### Phase 1 检查点
- [ ] 后端数据结构增强完成
- [ ] 前端来源标注组件创建
- [ ] 来源信息展示面板实现
- [ ] Playwright测试验证通过

### Phase 2 检查点
- [ ] 会话状态管理器实现
- [ ] 智能上下文提取集成
- [ ] 前端会话持久化完成
- [ ] 上下文连续性测试通过

### Phase 3 检查点
- [ ] 性能优化完成
- [ ] 用户体验增强实现
- [ ] 全面系统测试通过
- [ ] 生产环境验证完成

---

**🎯 立即行动**: 开始Phase 1 - AI回答工具来源标注实现
**📋 详细步骤**: 按照Task 1.1 → 1.2 → 1.3的顺序执行
**🧪 验证方式**: 使用Playwright MCP进行自动化测试验证
