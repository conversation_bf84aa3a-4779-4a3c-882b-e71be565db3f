<template>
  <div id="app" class="app-container">
    <!-- 顶部导航栏 -->
    <el-header class="app-header">
      <div class="header-content">
        <div class="logo-section">
          <!-- 移动端菜单按钮 -->
          <el-button
            v-if="isMobile"
            type="text"
            :icon="Menu"
            size="large"
            @click="toggleSidebar"
            class="mobile-menu-btn"
          />
          <el-icon class="logo-icon" :size="32" color="#409EFF">
            <WindPower />
          </el-icon>
          <h1 class="app-title">风机智能体 Web版</h1>
        </div>
        
        <div class="header-actions">
          <!-- 连接状态指示器 -->
          <el-badge
            :value="getConnectionStatusText()"
            :type="connectionStatus === 'connected' ? 'success' : (isConnecting || reconnectionInfo.isReconnecting ? 'warning' : 'danger')"
            class="connection-badge"
          >
            <el-button
              :type="connectionStatus === 'connected' ? 'success' : (isConnecting || reconnectionInfo.isReconnecting ? 'warning' : 'danger')"
              :icon="connectionStatus === 'connected' ? 'Connection' : (isConnecting || reconnectionInfo.isReconnecting ? 'Loading' : 'Close')"
              size="small"
              @click="toggleConnection"
              :loading="isConnecting || reconnectionInfo.isReconnecting"
            >
              <span>{{ getConnectionStatusIcon() }} {{ getConnectionStatusText() }}</span>
            </el-button>
          </el-badge>
          
          <!-- 设置按钮 -->
          <el-button type="primary" :icon="Setting" size="small" @click="showSettings = true">
            设置
          </el-button>
        </div>
      </div>
    </el-header>

    <!-- 主要内容区域 -->
    <el-container class="main-container">
      <!-- 移动端遮罩层 -->
      <div
        v-if="isMobile && sidebarOpen"
        class="sidebar-overlay"
        @click="closeSidebar"
      ></div>

      <!-- 侧边栏 -->
      <el-aside
        class="app-sidebar"
        :class="{ 'mobile-open': isMobile && sidebarOpen }"
        width="250px"
      >
        <el-menu
          default-active="chat"
          class="sidebar-menu"
          @select="handleMenuSelect"
        >
          <el-menu-item index="chat" @click="isMobile && closeSidebar()">
            <el-icon><ChatDotRound /></el-icon>
            <span>智能对话</span>
          </el-menu-item>
          <el-menu-item index="query" @click="isMobile && closeSidebar()">
            <el-icon><Search /></el-icon>
            <span>数据查询</span>
          </el-menu-item>
          <el-menu-item index="monitor" @click="isMobile && closeSidebar()">
            <el-icon><Monitor /></el-icon>
            <span>实时监控</span>
          </el-menu-item>
          <el-menu-item index="tools" @click="isMobile && closeSidebar()">
            <el-icon><Tools /></el-icon>
            <span>工具选择</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主内容区 -->
      <el-main class="app-main">
        <!-- 聊天界面 -->
        <div v-if="currentView === 'chat'" class="chat-container">
          <div class="chat-header">
            <div class="chat-header-left">
              <h2>🤖 智能对话</h2>
              <el-button
                type="text"
                size="small"
                @click="showSessionList = true"
                class="session-btn"
              >
                📋 会话列表 ({{ sessions.length }})
              </el-button>
            </div>
            <div class="chat-header-right">
              <el-button type="info" size="small" @click="showSearch = true" :icon="Search">搜索</el-button>
              <el-button type="success" size="small" @click="createNewSession">新建对话</el-button>
              <el-button type="primary" size="small" @click="clearChat">清空对话</el-button>
            </div>
          </div>
          
          <!-- 消息列表 -->
          <div class="message-list" ref="messageList">
            <div
              v-for="message in messages"
              :key="message.id"
              :class="['message-item', message.type, { 'processing': message.isProcessing, 'final': message.isFinal }]"
            >
              <div class="message-content">
                <!-- 增强的消息头部 -->
                <div class="message-header-enhanced" v-if="message.icon || message.type">
                  <div class="message-type-indicator">
                    <div class="message-icon-wrapper" :class="getMessageTypeClass(message.type)">
                      <span class="message-icon">{{ message.icon || getMessageTypeIcon(message.type) }}</span>
                      <div v-if="message.isProcessing" class="processing-spinner"></div>
                    </div>
                    <div class="message-type-info">
                      <span class="message-type-label">{{ getMessageTypeLabel(message.type) }}</span>
                      <span class="message-timestamp">{{ formatTime(message.timestamp) }}</span>
                    </div>
                  </div>
                  <div v-if="message.isProcessing" class="progress-bar">
                    <div class="progress-fill" :class="getProgressClass(message.type)"></div>
                  </div>
                </div>
                <!-- 错误消息特殊显示 -->
                <div v-if="message.isError" class="message-content">
                  <StructuredMessageRenderer :message-text="message.text" />
                  <div v-if="message.description" class="error-description">
                    {{ message.description }}
                  </div>
                  <div v-if="message.suggestions && message.suggestions.length > 0" class="error-suggestions">
                    <h4>💡 解决建议：</h4>
                    <ul>
                      <li v-for="suggestion in message.suggestions" :key="suggestion">
                        {{ suggestion }}
                      </li>
                    </ul>
                  </div>
                </div>
                <!-- 普通消息显示 - 🚀 范式转移：使用结构化渲染器 -->
                <div v-else class="message-text">
                  <StructuredMessageRenderer :message-text="message.text" />
                </div>

                <!-- 新的工具推荐组件 -->
                <ToolRecommendation
                  v-if="message.tools && showToolSelection"
                  :tools="recommendedTools"
                  :query-analysis="queryAnalysis"
                  @tool-selected="handleToolSelection"
                />

                <div class="message-time">{{ formatTime(message.timestamp) }}</div>
              </div>
            </div>

            <!-- 打字指示器 -->
            <div v-if="isTyping" class="message-item ai typing-indicator">
              <div class="message-content">
                <div class="typing-animation">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
                <div class="typing-text">AI正在思考中...</div>
              </div>
            </div>
          </div>
          
          <!-- 输入区域 -->
          <div class="chat-input">
            <div class="input-container">
              <!-- 语音输入按钮 -->
              <el-button
                :type="isListening ? 'danger' : 'info'"
                :icon="Microphone"
                circle
                size="large"
                @click="toggleVoiceInput"
                :disabled="connectionStatus !== 'connected'"
                class="voice-btn"
                :class="{ 'listening': isListening, 'pulse': isListening }"
                :title="isListening ? '点击停止录音' : '点击开始语音输入'"
              >
              </el-button>

              <!-- 文本输入框 -->
              <el-input
                v-model="inputMessage"
                :placeholder="isListening ? '正在听取语音...' : '请输入您的问题...'"
                @keyup.enter="sendMessage"
                :disabled="connectionStatus !== 'connected'"
                class="text-input"
              >
                <template #append>
                  <el-button
                    type="primary"
                    @click="sendMessage"
                    :disabled="!inputMessage.trim() || connectionStatus !== 'connected'"
                  >
                    发送
                  </el-button>
                </template>
              </el-input>
            </div>

            <!-- 语音识别状态提示 -->
            <div v-if="voiceStatus" class="voice-status" :class="voiceStatusType">
              <span class="status-icon">{{ voiceStatusIcon }}</span>
              <span class="status-text">{{ voiceStatus }}</span>
            </div>
          </div>
        </div>

        <!-- 其他视图占位 -->
        <div v-else class="placeholder-view">
          <el-empty description="功能开发中...">
            <el-button type="primary" @click="currentView = 'chat'">返回对话</el-button>
          </el-empty>
        </div>
      </el-main>
    </el-container>

    <!-- 来源数据面板 -->
    <SourceDataPanel
      v-if="currentView === 'chat'"
      :source-data="getSourceDataFromMessages()"
    />

    <!-- 设置对话框 -->
    <el-dialog v-model="showSettings" title="系统设置" width="500px">
      <el-form label-width="100px">
        <el-form-item label="后端地址">
          <el-input v-model="backendUrl" placeholder="http://localhost:8000" />
        </el-form-item>
        <el-form-item label="自动重连">
          <el-switch v-model="autoReconnect" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showSettings = false">取消</el-button>
        <el-button type="primary" @click="saveSettings">保存</el-button>
      </template>
    </el-dialog>

    <!-- 会话列表对话框 -->
    <el-dialog v-model="showSessionList" title="会话历史" width="600px" class="session-dialog">
      <div class="session-list">
        <div
          v-for="session in sessions"
          :key="session.id"
          :class="['session-item', { 'active': session.id === currentSessionId }]"
          @click="switchSession(session.id)"
        >
          <div class="session-content">
            <div class="session-title">{{ session.title }}</div>
            <div class="session-info">
              <span class="session-time">{{ formatSessionTime(session.updatedAt) }}</span>
              <span class="session-count">{{ session.messages?.length || 0 }} 条消息</span>
              <!-- 🚀 新增：会话上下文信息 -->
              <span v-if="session.contextSummary" class="session-context" :title="session.contextSummary">
                🧠 {{ session.contextSummary.substring(0, 30) }}{{ session.contextSummary.length > 30 ? '...' : '' }}
              </span>
            </div>
            <!-- 🚀 新增：智能洞察标签 -->
            <div v-if="session.intelligentInsights" class="session-insights">
              <el-tag
                v-for="tool in (session.intelligentInsights.recommended_tools || []).slice(0, 2)"
                :key="tool"
                size="small"
                type="info"
                class="insight-tag"
              >
                🔧 {{ tool }}
              </el-tag>
              <el-tag
                v-if="session.intelligentInsights.complexity_advice"
                size="small"
                type="warning"
                class="insight-tag"
                :title="session.intelligentInsights.complexity_advice"
              >
                💡 智能建议
              </el-tag>
            </div>
          </div>
          <div class="session-actions">
            <!-- 🚀 新增：继续对话按钮 -->
            <el-button
              v-if="session.id !== currentSessionId"
              type="primary"
              size="small"
              @click.stop="continueSession(session.id)"
              class="continue-btn"
            >
              继续对话
            </el-button>
            <!-- 🚀 新增：查看上下文按钮 -->
            <el-button
              type="info"
              size="small"
              @click.stop="openSessionContext(session.id)"
              class="context-btn"
            >
              上下文
            </el-button>
            <el-button
              type="danger"
              size="small"
              :icon="Delete"
              @click.stop="deleteSession(session.id)"
              v-if="sessions.length > 1"
            >
              删除
            </el-button>
          </div>
        </div>

        <div v-if="sessions.length === 0" class="empty-sessions">
          <el-empty description="暂无会话记录">
            <el-button type="primary" @click="createNewSession">创建第一个对话</el-button>
          </el-empty>
        </div>
      </div>

      <template #footer>
        <el-button @click="showSessionList = false">关闭</el-button>
        <el-button type="primary" @click="createNewSession">新建对话</el-button>
      </template>
    </el-dialog>

    <!-- 🚀 新增：会话上下文查看对话框 -->
    <el-dialog v-model="showSessionContext" title="会话上下文" width="700px" class="context-dialog">
      <div v-if="selectedSessionContext" class="context-content">
        <!-- 基本信息 -->
        <div class="context-section">
          <h4>📋 基本信息</h4>
          <div class="context-info">
            <p><strong>会话ID:</strong> {{ selectedSessionContext.session_id }}</p>
            <p><strong>消息数量:</strong> {{ selectedSessionContext.recent_messages?.length || 0 }}</p>
            <p><strong>会话状态:</strong>
              <el-tag :type="selectedSessionContext.conversation_state === 'active' ? 'success' : 'info'">
                {{ selectedSessionContext.conversation_state || 'unknown' }}
              </el-tag>
            </p>
          </div>
        </div>

        <!-- 上下文摘要 -->
        <div v-if="selectedSessionContext.context_summary" class="context-section">
          <h4>🧠 智能摘要</h4>
          <div class="context-summary">
            {{ selectedSessionContext.context_summary }}
          </div>
        </div>

        <!-- 关键实体 -->
        <div v-if="selectedSessionContext.key_entities && Object.keys(selectedSessionContext.key_entities).length > 0" class="context-section">
          <h4>🏷️ 关键实体</h4>
          <div class="context-entities">
            <el-tag
              v-for="(value, key) in selectedSessionContext.key_entities"
              :key="key"
              class="entity-tag"
              :title="`${key}: ${value}`"
            >
              {{ key }}: {{ typeof value === 'object' ? JSON.stringify(value) : value }}
            </el-tag>
          </div>
        </div>

        <!-- 智能洞察 -->
        <div v-if="selectedSessionContext.intelligent_insights" class="context-section">
          <h4>💡 智能洞察</h4>
          <div class="intelligent-insights">
            <div v-if="selectedSessionContext.intelligent_insights.recommended_tools?.length > 0" class="insight-item">
              <strong>推荐工具:</strong>
              <el-tag
                v-for="tool in selectedSessionContext.intelligent_insights.recommended_tools"
                :key="tool"
                type="info"
                size="small"
                class="tool-tag"
              >
                🔧 {{ tool }}
              </el-tag>
            </div>
            <div v-if="selectedSessionContext.intelligent_insights.complexity_advice" class="insight-item">
              <strong>复杂度建议:</strong>
              <span class="advice-text">{{ selectedSessionContext.intelligent_insights.complexity_advice }}</span>
            </div>
            <div v-if="selectedSessionContext.intelligent_insights.proactive_suggestions?.length > 0" class="insight-item">
              <strong>主动建议:</strong>
              <ul class="suggestions-list">
                <li v-for="suggestion in selectedSessionContext.intelligent_insights.proactive_suggestions" :key="suggestion">
                  {{ suggestion }}
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- 相关记忆 -->
        <div v-if="selectedSessionContext.relevant_memories?.length > 0" class="context-section">
          <h4>📚 相关记忆</h4>
          <div class="relevant-memories">
            <div v-for="memory in selectedSessionContext.relevant_memories" :key="memory.memory_id" class="memory-item">
              <div class="memory-header">
                <span class="memory-id">{{ memory.memory_id }}</span>
                <el-tag size="small" :type="memory.quality_score > 0.8 ? 'success' : (memory.quality_score > 0.6 ? 'warning' : 'info')">
                  质量: {{ (memory.quality_score * 100).toFixed(0) }}%
                </el-tag>
              </div>
              <div class="memory-content">
                <p><strong>问题:</strong> {{ memory.user_query }}</p>
                <p><strong>回答:</strong> {{ memory.ai_response }}</p>
              </div>
              <div class="memory-tags">
                <el-tag v-for="tag in memory.tags" :key="tag" size="small" type="info">{{ tag }}</el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="context-loading">
        <el-empty description="正在加载上下文信息...">
          <el-button type="primary" @click="loadSessionContext">重新加载</el-button>
        </el-empty>
      </div>

      <template #footer>
        <el-button @click="showSessionContext = false">关闭</el-button>
        <el-button type="primary" @click="continueSessionFromContext">基于此上下文继续对话</el-button>
      </template>
    </el-dialog>

    <!-- 搜索对话框 -->
    <el-dialog v-model="showSearch" title="搜索消息" width="700px" class="search-dialog">
      <div class="search-container">
        <!-- 搜索输入 -->
        <div class="search-input-section">
          <el-input
            v-model="searchQuery"
            placeholder="输入关键词搜索消息..."
            size="large"
            @input="performSearch"
            @keyup.enter="performSearch"
            clearable
            @clear="clearSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <!-- 过滤器 -->
        <div class="filter-section">
          <el-row :gutter="16">
            <el-col :span="12">
              <el-select v-model="messageTypeFilter" placeholder="消息类型" @change="filterMessages">
                <el-option label="全部类型" value="all" />
                <el-option label="用户消息" value="user" />
                <el-option label="AI回答" value="ai" />
                <el-option label="系统消息" value="system" />
              </el-select>
            </el-col>
            <el-col :span="12">
              <el-date-picker
                v-model="dateRangeFilter"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="filterMessages"
                size="default"
              />
            </el-col>
          </el-row>
          <el-button type="text" @click="clearFilters" size="small">清除过滤器</el-button>
        </div>

        <!-- 搜索结果 -->
        <div class="search-results" v-if="searchResults.length > 0">
          <div class="results-header">
            <span>找到 {{ searchResults.length }} 条结果</span>
          </div>
          <div class="results-list">
            <div
              v-for="(result, index) in searchResults"
              :key="index"
              class="result-item"
              @click="jumpToMessage(result)"
            >
              <div class="result-content">
                <div class="result-text" v-html="highlightSearchText(result.text)"></div>
                <div class="result-meta">
                  <span class="result-session">{{ result.sessionTitle }}</span>
                  <span class="result-type">{{ getMessageTypeLabel(result.type) }}</span>
                  <span class="result-time">{{ formatTime(result.timestamp) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 无结果 -->
        <div v-else-if="searchQuery.trim()" class="no-results">
          <el-empty description="未找到匹配的消息" />
        </div>
      </div>

      <template #footer>
        <el-button @click="showSearch = false">关闭</el-button>
        <el-button type="primary" @click="clearSearch">清除搜索</el-button>
      </template>
    </el-dialog>

    <!-- 重连失败对话框 -->
    <el-dialog
      v-model="showReconnectDialog"
      title="连接失败"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="reconnect-dialog">
        <div class="reconnect-icon">
          <el-icon size="48" color="#f56c6c">
            <Close />
          </el-icon>
        </div>
        <div class="reconnect-content">
          <h3>连接服务器失败</h3>
          <p>已尝试重连 {{ reconnectionInfo.retryCount }} 次，仍无法连接到服务器。</p>
          <div class="reconnect-info">
            <p><strong>可能的原因：</strong></p>
            <ul>
              <li>网络连接不稳定</li>
              <li>服务器暂时不可用</li>
              <li>防火墙阻止了连接</li>
            </ul>
          </div>
          <div class="reconnect-progress">
            <el-progress
              :percentage="Math.round(reconnectionInfo.progress * 100)"
              :color="reconnectionInfo.progress >= 1 ? '#f56c6c' : '#409eff'"
              :show-text="false"
            />
            <span class="progress-text">
              {{ reconnectionInfo.retryCount }} / {{ reconnectionInfo.maxRetries }} 次尝试
            </span>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showReconnectDialog = false">
            稍后重试
          </el-button>
          <el-button type="primary" @click="manualReconnect">
            立即重连
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Menu, Delete, Search, Microphone, Close } from '@element-plus/icons-vue'
import { ReconnectionManager } from './utils/reconnectionManager.js'
import ToolRecommendation from './components/ToolRecommendation.vue'
import StructuredMessageRenderer from './components/StructuredMessageRenderer.vue'
import SourceDataPanel from './components/SourceDataPanel.vue'
import katex from 'katex'
import 'katex/dist/katex.min.css'
import { marked } from 'marked'

export default {
  name: 'App',
  components: {
    ToolRecommendation,
    StructuredMessageRenderer,
    SourceDataPanel
  },
  setup() {
    // 初始化KaTeX
    window.katex = katex

    // 响应式数据
    const connectionStatus = ref('disconnected')
    const currentView = ref('chat')
    const showSettings = ref(false)
    const inputMessage = ref('')
    const messages = ref([])
    const messageList = ref(null)
    const isTyping = ref(false)
    const isConnecting = ref(false)
    const isMobile = ref(false)
    const sidebarOpen = ref(false)

    // 会话管理
    const sessions = ref([])
    const currentSessionId = ref(null)
    const showSessionList = ref(false)

    // 🚀 新增：会话上下文管理
    const showSessionContext = ref(false)
    const selectedSessionContext = ref(null)
    const selectedSessionId = ref(null)

    // 搜索和过滤
    const searchQuery = ref('')
    const showSearch = ref(false)
    const searchResults = ref([])
    const messageTypeFilter = ref('all')
    const dateRangeFilter = ref([])
    const filteredMessages = ref([])
    
    // 设置数据
    const backendUrl = ref('http://localhost:8000')
    const autoReconnect = ref(true)

    // 语音识别数据
    const isListening = ref(false)
    const voiceStatus = ref('')
    const voiceStatusType = ref('')
    const voiceStatusIcon = ref('')
    let speechRecognition = null

    // 工具推荐数据
    const recommendedTools = ref([])
    const queryAnalysis = ref(null)
    const showToolSelection = ref(false)

    // WebSocket连接
    const websocket = ref(null)
    let reconnectTimer = null
    const backendSessionId = ref(null)  // 后端分配的会话ID

    // 重连管理器相关状态
    const reconnectionInfo = ref({
      retryCount: 0,
      maxRetries: 10,
      isReconnecting: false,
      lastError: null,
      progress: 0,
      nextDelay: 0
    })
    const showReconnectDialog = ref(false)

    // 创建重连管理器
    const reconnectionManager = new ReconnectionManager({
      maxRetries: 10,
      initialDelay: 1000,
      maxDelay: 30000,
      backoffMultiplier: 1.5,
      autoReconnect: true,
      onReconnectAttempt: (retryCount, info) => {
        reconnectionInfo.value = { ...info }
        ElMessage.info({
          message: `🔄 正在进行第 ${retryCount} 次重连尝试...`,
          duration: 2000
        })
      },
      onReconnectSuccess: (retryCount) => {
        reconnectionInfo.value = reconnectionManager.getReconnectionInfo()
        ElMessage.success({
          message: `🎉 重连成功！总共尝试了 ${retryCount} 次`,
          duration: 3000
        })
      },
      onReconnectFailed: (retryCount, error, info) => {
        reconnectionInfo.value = { ...info }
        console.error(`重连失败 (${retryCount}/${info.maxRetries}):`, error.message)
      },
      onMaxRetriesReached: (retryCount, error) => {
        reconnectionInfo.value = reconnectionManager.getReconnectionInfo()
        showReconnectDialog.value = true
        ElMessage.error({
          message: `❌ 重连失败，已尝试 ${retryCount} 次。请检查网络连接。`,
          duration: 5000
        })
      },
      onStateChange: (state, info) => {
        reconnectionInfo.value = { ...info }
        console.log('🔄 重连状态变化:', state, info)
      }
    })
    
    // 初始化WebSocket连接
    const initWebSocket = () => {
      return new Promise((resolve, reject) => {
        try {
          isConnecting.value = true
          const wsUrl = backendUrl.value.replace('http', 'ws') + '/ws'
          websocket.value = new WebSocket(wsUrl)

          websocket.value.onopen = () => {
            connectionStatus.value = 'connected'
            isConnecting.value = false

            // 重连成功，重置重连管理器
            reconnectionManager.reset()

            ElMessage.success({
              message: '🎉 WebSocket连接成功',
              type: 'success',
              duration: 2000
            })

            // 暴露WebSocket到全局作用域用于调试
            window.websocket = websocket.value

            // 发送心跳
            sendHeartbeat()

            resolve()
          }

        websocket.value.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)
            console.log('🔔 收到WebSocket消息:', data)
            handleMessage(data)
          } catch (error) {
            console.error('消息解析错误:', error)
            ElMessage.error('消息解析失败')
          }
        }

        websocket.value.onclose = (event) => {
          connectionStatus.value = 'disconnected'
          isConnecting.value = false

          const reason = event.reason || '未知原因'
          const wasCleanClose = event.wasClean

          console.log('WebSocket连接关闭:', { code: event.code, reason, wasClean: wasCleanClose })

          if (!wasCleanClose && !reconnectionManager.getState().manualDisconnect) {
            ElMessage.warning({
              message: '⚠️ WebSocket连接意外断开',
              duration: 3000
            })

            // 启动智能重连
            if (autoReconnect.value && reconnectionManager.shouldReconnect()) {
              reconnectionManager.startReconnection(initWebSocket, new Error(`连接断开: ${reason}`))
            }
          } else {
            ElMessage.info({
              message: '🔌 WebSocket连接已关闭',
              duration: 2000
            })
          }

          reject(new Error(`连接关闭: ${reason}`))
        }

        websocket.value.onerror = (error) => {
          console.error('WebSocket错误:', error)
          isConnecting.value = false

          const errorMsg = '网络连接错误'
          ElMessage.error({
            message: `❌ ${errorMsg}`,
            duration: 4000
          })

          reject(new Error(errorMsg))
        }

        } catch (error) {
          console.error('WebSocket初始化失败:', error)
          isConnecting.value = false
          ElMessage.error('WebSocket初始化失败')
          reject(error)
        }
      })
    }
    
    // 处理接收到的消息
    const handleMessage = (data) => {
      console.log('📨 处理消息:', data.type, data)
      console.log('📊 当前messages数组长度:', messages.value.length)

      // 根据消息类型处理不同的响应
      if (data.type === 'processing_start') {
        // 开始处理消息
        const message = {
          id: Date.now(),
          type: 'system',
          text: data.message,
          timestamp: new Date(),
          isProcessing: true
        }
        messages.value.push(message)

        // 🚀 新增：保存上下文信息到当前会话
        if (data.context_info && currentSessionId.value) {
          const currentSession = sessions.value.find(s => s.id === currentSessionId.value)
          if (currentSession) {
            currentSession.contextSummary = data.context_info.context_summary
            currentSession.intelligentInsights = data.context_info.intelligent_insights
            currentSession.hasHistory = data.context_info.has_history
            currentSession.relevantMemoriesCount = data.context_info.relevant_memories_count || 0

            console.log('🧠 保存会话上下文信息:', {
              contextSummary: currentSession.contextSummary,
              relevantMemoriesCount: currentSession.relevantMemoriesCount
            })

            // 保存到本地存储
            saveSessionsToStorage()
          }
        }

      } else if (data.type === 'routing_analysis') {
        // 路由分析消息
        const message = {
          id: Date.now(),
          type: 'system',
          text: data.message,
          timestamp: new Date(),
          icon: '🧠'
        }
        messages.value.push(message)

      } else if (data.type === 'tool_recommendation') {
        // 工具推荐消息
        const message = {
          id: Date.now(),
          type: 'system',
          text: data.message,
          timestamp: new Date(),
          tools: data.tools,
          icon: '🔧'
        }
        messages.value.push(message)

        // 设置工具推荐数据
        recommendedTools.value = data.tools || []
        queryAnalysis.value = data.query_analysis || null
        showToolSelection.value = true

        // 保存后端分配的会话ID
        if (data.session_id) {
          backendSessionId.value = data.session_id
          console.log('📋 保存后端会话ID:', data.session_id)
        }

      } else if (data.type === 'data_retrieval') {
        // 数据检索消息
        const message = {
          id: Date.now(),
          type: 'system',
          text: data.message,
          timestamp: new Date(),
          icon: '📊'
        }
        messages.value.push(message)

      } else if (data.type === 'ai_analysis') {
        // AI分析消息
        const message = {
          id: Date.now(),
          type: 'system',
          text: data.message,
          timestamp: new Date(),
          icon: '🤖'
        }
        messages.value.push(message)

      } else if (data.type === 'streaming_start') {
        // 开始流式回答
        const message = {
          id: Date.now(),
          type: 'ai',
          text: '',
          timestamp: new Date(),
          isStreaming: true,
          isFinal: false
        }
        messages.value.push(message)
        isTyping.value = false // 关闭打字状态，开始流式显示

      } else if (data.type === 'streaming_chunk') {
        // 流式回答块
        const lastMessage = messages.value[messages.value.length - 1]
        if (lastMessage && lastMessage.isStreaming) {
          lastMessage.text = data.message
          lastMessage.isFinal = data.is_complete
        }

      } else if (data.type === 'streaming_complete') {
        // 流式回答完成
        const lastMessage = messages.value[messages.value.length - 1]
        if (lastMessage && lastMessage.isStreaming) {
          lastMessage.text = data.message
          lastMessage.isStreaming = false
          lastMessage.isFinal = true
        }

      } else if (data.type === 'final_answer') {
        // 最终回答（兼容旧版本）
        const message = {
          id: Date.now(),
          type: 'ai',
          text: data.message,
          timestamp: new Date(),
          isFinal: true
        }
        messages.value.push(message)
        isTyping.value = false // 关闭打字状态

      } else if (data.type === 'processing_complete') {
        // 处理完成
        const message = {
          id: Date.now(),
          type: 'system',
          text: data.message,
          timestamp: new Date(),
          icon: '✅'
        }
        messages.value.push(message)
        isTyping.value = false // 关闭打字状态

      } else if (data.type === 'error') {
        // 错误消息 - 用户友好处理
        const message = {
          id: Date.now(),
          type: 'error',
          text: data.user_message || data.message,
          description: data.description,
          suggestions: data.suggestions,
          severity: data.severity || 'error',
          timestamp: new Date(),
          icon: data.icon || '❌',
          isError: true
        }
        messages.value.push(message)
        isTyping.value = false // 关闭打字状态

        // 显示错误通知
        const notificationType = data.severity === 'warning' ? 'warning' : 'error'
        ElMessage({
          message: data.user_message || data.message,
          type: notificationType,
          duration: 5000,
          showClose: true
        })

      } else {
        // 其他类型消息
        const message = {
          id: Date.now(),
          type: data.type === 'system' ? 'system' : 'ai',
          text: data.message,
          timestamp: new Date()
        }
        messages.value.push(message)
      }

      // 滚动到底部
      nextTick(() => {
        if (messageList.value) {
          messageList.value.scrollTop = messageList.value.scrollHeight
        }
      })
    }
    
    // 发送消息
    const sendMessage = () => {
      if (!inputMessage.value.trim() || connectionStatus.value !== 'connected') {
        if (connectionStatus.value !== 'connected') {
          ElMessage.warning('请先连接到服务器')
        }
        return
      }

      // 添加用户消息
      const userMessage = {
        id: Date.now(),
        type: 'user',
        text: inputMessage.value,
        timestamp: new Date()
      }
      messages.value.push(userMessage)

      // 设置打字状态
      isTyping.value = true

      // 发送到后端
      const messageData = {
        type: 'chat',
        message: inputMessage.value
      }

      websocket.value.send(JSON.stringify(messageData))

      // 清空输入
      inputMessage.value = ''

      // 滚动到底部
      nextTick(() => {
        scrollToBottom()
      })
    }

    // 滚动到底部的辅助函数
    const scrollToBottom = () => {
      if (messageList.value) {
        messageList.value.scrollTop = messageList.value.scrollHeight
      }
    }

    // 检测屏幕尺寸
    const checkScreenSize = () => {
      isMobile.value = window.innerWidth <= 768
      if (!isMobile.value) {
        sidebarOpen.value = false
      }
    }

    // 切换侧边栏
    const toggleSidebar = () => {
      if (isMobile.value) {
        sidebarOpen.value = !sidebarOpen.value
      }
    }

    // 关闭侧边栏
    const closeSidebar = () => {
      sidebarOpen.value = false
    }

    // 会话管理函数
    const createNewSession = () => {
      const sessionId = Date.now().toString()
      const newSession = {
        id: sessionId,
        title: '新对话',
        messages: [],
        createdAt: new Date(),
        updatedAt: new Date()
      }

      sessions.value.unshift(newSession)
      currentSessionId.value = sessionId
      messages.value = []

      saveSessionsToStorage()
      ElMessage.success('已创建新对话')
    }

    const switchSession = (sessionId) => {
      // 保存当前会话
      saveCurrentSession()

      // 切换到新会话
      const session = sessions.value.find(s => s.id === sessionId)
      if (session) {
        currentSessionId.value = sessionId
        messages.value = session.messages || []
        session.updatedAt = new Date()

        saveSessionsToStorage()
        showSessionList.value = false

        nextTick(() => {
          scrollToBottom()
        })
      }
    }

    const deleteSession = (sessionId) => {
      const index = sessions.value.findIndex(s => s.id === sessionId)
      if (index > -1) {
        sessions.value.splice(index, 1)

        if (currentSessionId.value === sessionId) {
          if (sessions.value.length > 0) {
            switchSession(sessions.value[0].id)
          } else {
            createNewSession()
          }
        }

        saveSessionsToStorage()
        ElMessage.success('会话已删除')
      }
    }

    // 🚀 新增：继续会话功能
    const continueSession = (sessionId) => {
      // 切换到指定会话
      switchSession(sessionId)
      showSessionList.value = false

      // 发送继续对话的提示消息
      const continueMessage = {
        id: Date.now(),
        type: 'system',
        text: '💬 已切换到此会话，您可以继续之前的对话。AI将基于历史上下文为您提供连贯的回答。',
        timestamp: new Date().toISOString(),
        isSystemMessage: true
      }

      messages.value.push(continueMessage)
      nextTick(() => {
        scrollToBottom()
      })

      ElMessage.success('已切换到指定会话，可以继续对话')
    }

    // 🚀 新增：显示会话上下文
    const openSessionContext = async (sessionId) => {
      selectedSessionId.value = sessionId
      showSessionContext.value = true

      // 尝试从后端获取会话上下文
      await loadSessionContext()
    }

    // 🚀 新增：加载会话上下文
    const loadSessionContext = async () => {
      if (!selectedSessionId.value) return

      try {
        // 这里应该调用后端API获取会话上下文
        // 暂时使用模拟数据
        const session = sessions.value.find(s => s.id === selectedSessionId.value)
        if (session) {
          selectedSessionContext.value = {
            session_id: session.id,
            context_summary: session.contextSummary || '暂无智能摘要',
            recent_messages: session.messages || [],
            key_entities: session.keyEntities || {},
            user_preferences: session.userPreferences || {},
            conversation_state: 'active',
            relevant_memories: session.relevantMemories || [],
            intelligent_insights: session.intelligentInsights || {}
          }
        }
      } catch (error) {
        console.error('加载会话上下文失败:', error)
        ElMessage.error('加载会话上下文失败')
      }
    }

    // 🚀 新增：基于上下文继续对话
    const continueSessionFromContext = () => {
      if (selectedSessionId.value) {
        continueSession(selectedSessionId.value)
        showSessionContext.value = false
      }
    }

    const saveCurrentSession = () => {
      if (currentSessionId.value && messages.value.length > 0) {
        const session = sessions.value.find(s => s.id === currentSessionId.value)
        if (session) {
          session.messages = [...messages.value]
          session.updatedAt = new Date()

          // 更新会话标题（使用第一条用户消息）
          const firstUserMessage = messages.value.find(m => m.type === 'user')
          if (firstUserMessage && session.title === '新对话') {
            session.title = firstUserMessage.text.substring(0, 20) + (firstUserMessage.text.length > 20 ? '...' : '')
          }
        }
      }
    }

    const saveSessionsToStorage = () => {
      try {
        localStorage.setItem('turbine-chat-sessions', JSON.stringify(sessions.value))
        localStorage.setItem('turbine-current-session', currentSessionId.value || '')
      } catch (error) {
        console.error('保存会话失败:', error)
      }
    }

    const loadSessionsFromStorage = () => {
      try {
        const savedSessions = localStorage.getItem('turbine-chat-sessions')
        const savedCurrentSession = localStorage.getItem('turbine-current-session')

        if (savedSessions) {
          sessions.value = JSON.parse(savedSessions)
          // 转换日期字符串为Date对象
          sessions.value.forEach(session => {
            session.createdAt = new Date(session.createdAt)
            session.updatedAt = new Date(session.updatedAt)
          })
        }

        if (savedCurrentSession && sessions.value.find(s => s.id === savedCurrentSession)) {
          switchSession(savedCurrentSession)
        } else if (sessions.value.length > 0) {
          switchSession(sessions.value[0].id)
        } else {
          createNewSession()
        }
      } catch (error) {
        console.error('加载会话失败:', error)
        createNewSession()
      }
    }

    // 搜索和过滤函数
    const performSearch = () => {
      if (!searchQuery.value.trim()) {
        searchResults.value = []
        return
      }

      const query = searchQuery.value.toLowerCase()
      const results = []

      // 搜索当前会话
      messages.value.forEach((message, index) => {
        if (message.text && message.text.toLowerCase().includes(query)) {
          results.push({
            ...message,
            messageIndex: index,
            sessionId: currentSessionId.value,
            sessionTitle: sessions.value.find(s => s.id === currentSessionId.value)?.title || '当前会话'
          })
        }
      })

      // 搜索其他会话
      sessions.value.forEach(session => {
        if (session.id !== currentSessionId.value && session.messages) {
          session.messages.forEach((message, index) => {
            if (message.text && message.text.toLowerCase().includes(query)) {
              results.push({
                ...message,
                messageIndex: index,
                sessionId: session.id,
                sessionTitle: session.title
              })
            }
          })
        }
      })

      // 按时间排序
      results.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      searchResults.value = results
    }

    const highlightSearchText = (text) => {
      if (!searchQuery.value.trim()) return text

      const query = searchQuery.value.trim()
      const regex = new RegExp(`(${query})`, 'gi')
      return text.replace(regex, '<mark class="search-highlight">$1</mark>')
    }

    const jumpToMessage = (result) => {
      if (result.sessionId !== currentSessionId.value) {
        // 切换到对应会话
        switchSession(result.sessionId)
      }

      // 滚动到对应消息
      nextTick(() => {
        const messageElements = document.querySelectorAll('.message-item')
        if (messageElements[result.messageIndex]) {
          messageElements[result.messageIndex].scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          })

          // 高亮显示
          messageElements[result.messageIndex].classList.add('message-highlight')
          setTimeout(() => {
            messageElements[result.messageIndex].classList.remove('message-highlight')
          }, 3000)
        }
      })

      showSearch.value = false
    }

    const filterMessages = () => {
      let filtered = [...messages.value]

      // 按消息类型过滤
      if (messageTypeFilter.value !== 'all') {
        filtered = filtered.filter(msg => msg.type === messageTypeFilter.value)
      }

      // 按日期范围过滤
      if (dateRangeFilter.value && dateRangeFilter.value.length === 2) {
        const [startDate, endDate] = dateRangeFilter.value
        filtered = filtered.filter(msg => {
          const msgDate = new Date(msg.timestamp)
          return msgDate >= startDate && msgDate <= endDate
        })
      }

      filteredMessages.value = filtered
    }

    const clearFilters = () => {
      messageTypeFilter.value = 'all'
      dateRangeFilter.value = []
      filteredMessages.value = []
    }

    const clearSearch = () => {
      searchQuery.value = ''
      searchResults.value = []
    }

    const getMessageTypeLabel = (type) => {
      const labels = {
        'user': '用户',
        'ai': 'AI',
        'system': '系统',
        'processing': '处理中',
        'processing_start': '开始处理',
        'routing_analysis': '路由分析',
        'tool_recommendation': '工具推荐',
        'data_retrieval': '数据检索',
        'ai_analysis': 'AI分析',
        'streaming_start': '开始回答',
        'streaming_chunk': '正在回答',
        'streaming_complete': '回答完成',
        'final_answer': '最终回答',
        'final': '最终回答'
      }
      return labels[type] || type
    }

    // 获取消息类型图标
    const getMessageTypeIcon = (type) => {
      const icons = {
        'user': '👤',
        'ai': '🤖',
        'system': '⚙️',
        'processing': '⏳',
        'processing_start': '🚀',
        'routing_analysis': '🧠',
        'tool_recommendation': '🔧',
        'data_retrieval': '📊',
        'ai_analysis': '🤖',
        'streaming_start': '💬',
        'streaming_chunk': '✍️',
        'streaming_complete': '✅',
        'final_answer': '✅',
        'final': '✅'
      }
      return icons[type] || '💬'
    }

    // 获取消息类型样式类
    const getMessageTypeClass = (type) => {
      const classes = {
        'user': 'type-user',
        'ai': 'type-ai',
        'system': 'type-system',
        'processing_start': 'type-processing',
        'routing_analysis': 'type-analysis',
        'tool_recommendation': 'type-tools',
        'data_retrieval': 'type-data',
        'ai_analysis': 'type-ai-analysis',
        'streaming_start': 'type-streaming',
        'streaming_chunk': 'type-streaming',
        'streaming_complete': 'type-final',
        'final_answer': 'type-final',
        'final': 'type-final'
      }
      return classes[type] || 'type-default'
    }

    // 获取进度条样式类
    const getProgressClass = (type) => {
      const classes = {
        'processing_start': 'progress-start',
        'routing_analysis': 'progress-analysis',
        'tool_recommendation': 'progress-tools',
        'data_retrieval': 'progress-data',
        'ai_analysis': 'progress-ai'
      }
      return classes[type] || 'progress-default'
    }
    
    // 发送心跳
    const sendHeartbeat = () => {
      if (websocket.value && websocket.value.readyState === WebSocket.OPEN) {
        websocket.value.send(JSON.stringify({ type: 'ping' }))
      }
    }
    
    // 切换连接状态
    const toggleConnection = () => {
      if (connectionStatus.value === 'connected') {
        // 手动断开连接
        reconnectionManager.markManualDisconnect()
        websocket.value.close()
      } else {
        // 手动连接
        reconnectionManager.reset()
        initWebSocket()
      }
    }

    // 手动重连
    const manualReconnect = () => {
      showReconnectDialog.value = false
      ElMessage.info('🔄 开始手动重连...')
      reconnectionManager.manualReconnect(initWebSocket)
    }

    // 获取连接状态文本
    const getConnectionStatusText = () => {
      if (connectionStatus.value === 'connected') {
        return '已连接'
      } else if (isConnecting.value) {
        return '连接中'
      } else if (reconnectionInfo.value.isReconnecting) {
        return `重连中 (${reconnectionInfo.value.retryCount}/${reconnectionInfo.value.maxRetries})`
      } else {
        return '连接中'
      }
    }

    // 获取连接状态图标
    const getConnectionStatusIcon = () => {
      if (connectionStatus.value === 'connected') {
        return '🟢'
      } else if (isConnecting.value || reconnectionInfo.value.isReconnecting) {
        return '🟡'
      } else {
        return '🔴'
      }
    }
    
    // 菜单选择
    const handleMenuSelect = (index) => {
      currentView.value = index
    }
    
    // 清空对话
    const clearChat = () => {
      messages.value = []
      saveCurrentSession()
      ElMessage.success('对话已清空')
    }
    
    // 保存设置
    const saveSettings = () => {
      showSettings.value = false
      ElMessage.success('设置已保存')
      
      // 重新连接WebSocket
      if (websocket.value) {
        websocket.value.close()
      }
      setTimeout(() => {
        initWebSocket()
      }, 1000)
    }
    
    // 格式化时间
    const formatTime = (timestamp) => {
      return new Date(timestamp).toLocaleTimeString()
    }

    // 格式化会话时间
    const formatSessionTime = (timestamp) => {
      const now = new Date()
      const date = new Date(timestamp)
      const diffMs = now - date
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

      if (diffDays === 0) {
        return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
      } else if (diffDays === 1) {
        return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
      } else if (diffDays < 7) {
        return diffDays + '天前'
      } else {
        return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
      }
    }

    // 格式化消息文本 - 使用预处理与占位符策略
    const formatMessageText = (text) => {
      if (!text) return ''

      console.log('🚨 formatMessageText被调用，输入文本长度:', text.length)
      console.log('🚨 输入文本前200字符:', text.substring(0, 200))

      // 1. 预处理特殊格式 - 修复 'code [ref=xxx]: text' 问题
      let preprocessed = text.replace(/code\s*\[ref=[^\]]+\]:\s*([^\n]+)/g, '`$1`')

      // 2. 表格预处理与占位符策略
      const tablePlaceholders = []

      console.log('🚨 开始表格处理，预处理后文本长度:', preprocessed.length)
      console.log('🚨 预处理后文本是否包含管道符:', preprocessed.includes('|'))

      // 数据清洗预处理步骤：将AI生成的单行表格转换为标准多行格式
      // 核心思想：先用简单工具把脏数据洗干净，再送入专业工具处理

      console.log('🧹 开始数据清洗预处理...')

      // 检测并转换单行表格格式：|| 作为行分隔符
      if (preprocessed.includes('||') && preprocessed.includes('|')) {
        console.log('🔍 检测到单行表格格式，执行数据清洗...')

        // 简单而有效的正则表达式：将 || 替换为换行符
        // 这样就能将单行表格转换为多行表格，供后续逻辑处理
        preprocessed = preprocessed.replace(/\|\|/g, '|\n|')

        console.log('🧹 数据清洗完成，转换后的文本预览:')
        console.log(preprocessed.substring(0, 500) + '...')
      }

      // 使用统一的多行表格处理逻辑（现在可以处理清洗后的数据）
      const lines = preprocessed.split('\n')
      console.log('🚨 分割后行数:', lines.length)
      const processedLines = []
      let currentTable = []
      let inTable = false

      console.log('🚨 开始逐行处理表格...')
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim()

        // 检查是否是表格行（包含管道符且不是代码块）
        const isTableRow = line.includes('|') && !line.startsWith('```') && !line.startsWith('    ')

        if (line.includes('|')) {
          console.log(`🚨 第${i}行包含管道符:`, line.substring(0, 50))
          console.log(`🚨 isTableRow判断结果:`, isTableRow)
        }

        if (isTableRow) {
          if (!inTable) {
            // 开始新表格
            inTable = true
            currentTable = [line]
          } else {
            // 继续当前表格
            currentTable.push(line)
          }
        } else {
          if (inTable) {
            // 表格结束，处理完整表格
            if (currentTable.length >= 2) { // 至少需要表头和一行数据
              const placeholder = `__TABLE_PLACEHOLDER_${tablePlaceholders.length}__`
              const tableMarkdown = currentTable.join('\n')

              console.log('🔍 完整表格处理:', {
                tableLines: currentTable.length,
                placeholder,
                preview: tableMarkdown.substring(0, 100) + '...'
              })

              // 预渲染表格为HTML
              try {
                const tableHtml = marked(tableMarkdown, {
                  gfm: true,
                  tables: true,
                  breaks: false
                })
                console.log('🔍 表格HTML渲染结果:', tableHtml.substring(0, 200) + '...')
                tablePlaceholders.push(tableHtml)
              } catch (error) {
                console.warn('表格预渲染失败:', error)
                tablePlaceholders.push(`<pre>${tableMarkdown}</pre>`)
              }

              processedLines.push(placeholder)
            } else {
              // 不是有效表格，保持原样
              processedLines.push(...currentTable)
            }

            currentTable = []
            inTable = false
          }

          // 添加非表格行
          processedLines.push(line)
        }
      }

      // 处理文件末尾的表格
      if (inTable && currentTable.length >= 2) {
        const placeholder = `__TABLE_PLACEHOLDER_${tablePlaceholders.length}__`
        const tableMarkdown = currentTable.join('\n')

        console.log('🔍 文件末尾表格处理:', {
          tableLines: currentTable.length,
          placeholder,
          preview: tableMarkdown.substring(0, 100) + '...'
        })

        try {
          const tableHtml = marked(tableMarkdown, {
            gfm: true,
            tables: true,
            breaks: false
          })
          tablePlaceholders.push(tableHtml)
        } catch (error) {
          console.warn('文件末尾表格预渲染失败:', error)
          tablePlaceholders.push(`<pre>${tableMarkdown}</pre>`)
        }

        processedLines.push(placeholder)
      } else if (inTable) {
        // 不是有效表格，保持原样
        processedLines.push(...currentTable)
      }

      // 重新组合处理后的文本
      preprocessed = processedLines.join('\n')

      // 3. 文本格式优化 - 确保段落和列表正确分隔
      preprocessed = preprocessed
        // 确保列表项前有换行
        .replace(/([^\n])\n([-*+]\s)/g, '$1\n\n$2')
        // 确保数字列表前有换行
        .replace(/([^\n])\n(\d+\.\s)/g, '$1\n\n$2')
        // 修复连续文本缺少段落分隔的问题
        .replace(/([。！？])\s*([A-Z\u4e00-\u9fff])/g, '$1\n\n$2')
        // 清理多余的换行符
        .replace(/\n{3,}/g, '\n\n')

      // 4. 处理数学公式 - 在Markdown解析前处理，避免被解析器破坏
      const mathPlaceholders = []

      // 处理块级数学公式
      preprocessed = preprocessed.replace(/\$\$([\s\S]*?)\$\$/g, (match, formula) => {
        const placeholder = `__MATH_BLOCK_${mathPlaceholders.length}__`
        try {
          if (window.katex) {
            mathPlaceholders.push(`<div class="math-formula">${window.katex.renderToString(formula.trim(), {
              displayMode: true,
              throwOnError: false
            })}</div>`)
          } else {
            mathPlaceholders.push(`<div class="math-fallback">$$${formula.trim()}$$</div>`)
          }
        } catch (error) {
          console.warn('数学公式渲染失败:', error)
          mathPlaceholders.push(`<div class="math-fallback">$$${formula.trim()}$$</div>`)
        }
        return placeholder
      })

      // 处理行内数学公式
      preprocessed = preprocessed.replace(/\$([^$\n]+)\$/g, (match, formula) => {
        const placeholder = `__MATH_INLINE_${mathPlaceholders.length}__`
        try {
          if (window.katex) {
            mathPlaceholders.push(`<span class="math-inline">${window.katex.renderToString(formula.trim(), {
              displayMode: false,
              throwOnError: false
            })}</span>`)
          } else {
            mathPlaceholders.push(`<span class="math-fallback">$${formula.trim()}$</span>`)
          }
        } catch (error) {
          console.warn('行内数学公式渲染失败:', error)
          mathPlaceholders.push(`<span class="math-fallback">$${formula.trim()}$</span>`)
        }
        return placeholder
      })

      // 5. 使用marked.js解析"干净"的文本（不含表格）
      try {
        // 配置marked选项 - 优化文本格式处理
        marked.setOptions({
          breaks: false,       // 关闭自动换行，避免文本挤压
          gfm: true,          // 支持GitHub风格Markdown
          tables: false,      // 关闭表格解析（我们已经预处理了）
          sanitize: false,    // 不清理HTML（我们需要保留数学公式）
          smartLists: true,   // 智能列表
          smartypants: false, // 不转换引号
          pedantic: false     // 不使用严格模式
        })

        // 使用marked解析不含表格的Markdown
        let htmlContent = marked(preprocessed)

        // 调试：输出marked.js的解析结果
        console.log('🔍 marked.js解析结果:', htmlContent.substring(0, 500))

        // 6. 恢复表格占位符
        tablePlaceholders.forEach((tableHtml, index) => {
          const placeholder = `__TABLE_PLACEHOLDER_${index}__`
          const placeholderWithoutUnderscores = `TABLE_PLACEHOLDER_${index}` // marked.js会移除下划线

          // 原始占位符替换
          htmlContent = htmlContent.replace(placeholder, tableHtml)

          // 处理可能被marked包装的占位符
          htmlContent = htmlContent.replace(`<p>${placeholder}</p>`, tableHtml)

          // 处理被marked转换为strong标签的占位符（修复：包含完整的占位符格式）
          htmlContent = htmlContent.replace(`<strong>${placeholder}</strong>`, tableHtml)
          htmlContent = htmlContent.replace(`<p><strong>${placeholder}</strong></p>`, tableHtml)

          // 处理被marked转换为em标签的占位符
          htmlContent = htmlContent.replace(`<em>${placeholder}</em>`, tableHtml)
          htmlContent = htmlContent.replace(`<p><em>${placeholder}</em></p>`, tableHtml)

          // 🔧 关键修复：处理marked.js移除下划线后的占位符
          htmlContent = htmlContent.replace(placeholderWithoutUnderscores, tableHtml)
          htmlContent = htmlContent.replace(`<p>${placeholderWithoutUnderscores}</p>`, tableHtml)
          htmlContent = htmlContent.replace(`<strong>${placeholderWithoutUnderscores}</strong>`, tableHtml)
          htmlContent = htmlContent.replace(`<p><strong>${placeholderWithoutUnderscores}</strong></p>`, tableHtml)
          htmlContent = htmlContent.replace(`<em>${placeholderWithoutUnderscores}</em>`, tableHtml)
          htmlContent = htmlContent.replace(`<p><em>${placeholderWithoutUnderscores}</em></p>`, tableHtml)

          console.log(`🔧 表格占位符恢复: ${placeholder} (和 ${placeholderWithoutUnderscores}) -> ${tableHtml.substring(0, 100)}...`)
        })

        // 7. 恢复数学公式占位符
        mathPlaceholders.forEach((mathHtml, index) => {
          // 原始占位符
          htmlContent = htmlContent.replace(`__MATH_BLOCK_${index}__`, mathHtml)
          htmlContent = htmlContent.replace(`__MATH_INLINE_${index}__`, mathHtml)

          // marked.js可能将占位符转换为HTML标签，需要处理这些情况
          htmlContent = htmlContent.replace(`<strong>MATH_BLOCK_${index}</strong>`, mathHtml)
          htmlContent = htmlContent.replace(`<strong>MATH_INLINE_${index}</strong>`, mathHtml)
          htmlContent = htmlContent.replace(`<em>MATH_BLOCK_${index}</em>`, mathHtml)
          htmlContent = htmlContent.replace(`<em>MATH_INLINE_${index}</em>`, mathHtml)
          htmlContent = htmlContent.replace(`<p>__MATH_BLOCK_${index}__</p>`, mathHtml)
          htmlContent = htmlContent.replace(`<p>__MATH_INLINE_${index}__</p>`, mathHtml)
        })

        // 8. 清理和优化HTML输出
        htmlContent = htmlContent
          // 移除多余的空段落
          .replace(/<p>\s*<\/p>/g, '')
          // 清理数学公式周围的段落标签
          .replace(/<p>(<div class="math-formula">[\s\S]*?<\/div>)<\/p>/g, '$1')
          .replace(/<p>(<span class="math-inline">[\s\S]*?<\/span>)<\/p>/g, '$1')
          // 确保段落间有适当的间距
          .replace(/<\/p>\s*<p>/g, '</p><p>')

        return htmlContent

      } catch (error) {
        console.error('Markdown解析失败:', error)
        // 降级处理：返回简单的HTML转换
        return preprocessed
          .replace(/\n/g, '<br>')
          .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
          .replace(/\*(.*?)\*/g, '<em>$1</em>')
          .replace(/`(.*?)`/g, '<code>$1</code>')
      }
    }

    // 获取工具图标
    const getToolIcon = (toolName) => {
      const iconMap = {
        'wind-turbine-db': '🗄️',
        'context7': '📚',
        'fetch': '🌐',
        'filesystem': '📁',
        'pdf-processor': '📄',
        'default': '🔧'
      }
      return iconMap[toolName] || iconMap.default
    }

    // 选择工具
    const selectTool = (tool) => {
      console.log('🔧 选择工具:', tool)
      console.log('🔌 WebSocket状态:', websocket.value?.readyState)
      console.log('📋 前端会话ID:', currentSessionId.value)
      console.log('📋 后端会话ID:', backendSessionId.value)

      ElMessage.success(`已选择工具: ${tool.name}`)

      // 发送工具选择消息 - 修复格式以匹配后端期望
      const toolMessage = {
        type: 'tool_selection',
        selected_tools: [tool.name],  // 后端期望的是数组格式
        session_id: backendSessionId.value || 'default',  // 使用后端分配的会话ID
        timestamp: new Date().toISOString()
      }

      console.log('📤 发送工具选择消息:', toolMessage)

      if (websocket.value && websocket.value.readyState === WebSocket.OPEN) {
        websocket.value.send(JSON.stringify(toolMessage))
        console.log('✅ 工具选择消息已发送')
      } else {
        console.error('❌ WebSocket连接状态异常:', websocket.value?.readyState)
        ElMessage.error('WebSocket连接已断开，无法发送工具选择')
      }
    }

    // 处理工具选择
    const handleToolSelection = (selection) => {
      console.log('🔧 工具选择:', selection)

      // 发送选择到后端
      if (websocket.value && websocket.value.readyState === WebSocket.OPEN) {
        const toolMessage = {
          type: 'tool_selection',
          selected_tools: selection.tools,
          mode: selection.mode,
          session_id: backendSessionId.value || 'default',
          timestamp: new Date().toISOString()
        }

        websocket.value.send(JSON.stringify(toolMessage))
        console.log('📤 发送工具选择消息:', toolMessage)
      } else {
        console.error('❌ WebSocket连接状态异常:', websocket.value?.readyState)
        ElMessage.error('WebSocket连接已断开，无法发送工具选择')
        return
      }

      // 隐藏工具选择界面
      showToolSelection.value = false

      // 显示选择反馈
      if (selection.mode === 'ai-only') {
        ElMessage.info('已选择仅使用AI分析')
      } else {
        ElMessage.success(`已选择 ${selection.tools.length} 个工具`)
      }
    }

    // 语音识别功能
    const initSpeechRecognition = () => {
      if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
        console.warn('浏览器不支持语音识别')
        return false
      }

      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
      speechRecognition = new SpeechRecognition()

      speechRecognition.continuous = false
      speechRecognition.interimResults = true
      speechRecognition.lang = 'zh-CN'

      speechRecognition.onstart = () => {
        isListening.value = true
        voiceStatus.value = '正在听取语音...'
        voiceStatusType.value = 'listening'
        voiceStatusIcon.value = '🎤'
      }

      speechRecognition.onresult = (event) => {
        let finalTranscript = ''
        let interimTranscript = ''

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript
          if (event.results[i].isFinal) {
            finalTranscript += transcript
          } else {
            interimTranscript += transcript
          }
        }

        if (finalTranscript) {
          inputMessage.value = finalTranscript
          voiceStatus.value = `识别结果: ${finalTranscript}`
          voiceStatusType.value = 'success'
          voiceStatusIcon.value = '✅'

          // 3秒后清除状态
          setTimeout(() => {
            voiceStatus.value = ''
          }, 3000)
        } else if (interimTranscript) {
          voiceStatus.value = `正在识别: ${interimTranscript}`
          voiceStatusType.value = 'processing'
          voiceStatusIcon.value = '🔄'
        }
      }

      speechRecognition.onerror = (event) => {
        isListening.value = false
        let errorMessage = '语音识别出错'

        switch (event.error) {
          case 'no-speech':
            errorMessage = '未检测到语音，请重试'
            break
          case 'audio-capture':
            errorMessage = '无法访问麦克风'
            break
          case 'not-allowed':
            errorMessage = '麦克风权限被拒绝'
            break
          case 'network':
            errorMessage = '网络错误，请检查连接'
            break
          default:
            errorMessage = `语音识别错误: ${event.error}`
        }

        voiceStatus.value = errorMessage
        voiceStatusType.value = 'error'
        voiceStatusIcon.value = '❌'

        // 5秒后清除错误状态
        setTimeout(() => {
          voiceStatus.value = ''
        }, 5000)
      }

      speechRecognition.onend = () => {
        isListening.value = false
        if (voiceStatusType.value === 'listening' || voiceStatusType.value === 'processing') {
          voiceStatus.value = '语音识别已停止'
          voiceStatusType.value = 'info'
          voiceStatusIcon.value = 'ℹ️'

          setTimeout(() => {
            voiceStatus.value = ''
          }, 2000)
        }
      }

      return true
    }

    // 切换语音输入
    const toggleVoiceInput = () => {
      if (!speechRecognition) {
        if (!initSpeechRecognition()) {
          ElMessage.error('您的浏览器不支持语音识别功能')
          return
        }
      }

      if (isListening.value) {
        speechRecognition.stop()
      } else {
        try {
          speechRecognition.start()
        } catch (error) {
          console.error('启动语音识别失败:', error)
          ElMessage.error('启动语音识别失败，请重试')
        }
      }
    }

    // 生命周期
    onMounted(() => {
      loadSessionsFromStorage()
      initWebSocket()
      checkScreenSize()
      initSpeechRecognition()
      window.addEventListener('resize', checkScreenSize)

      // 页面卸载前保存会话
      window.addEventListener('beforeunload', saveCurrentSession)
    })

    onUnmounted(() => {
      saveCurrentSession()

      // 标记为手动断开，停止重连
      reconnectionManager.markManualDisconnect()

      if (websocket.value) {
        websocket.value.close()
      }
      if (reconnectTimer) {
        clearTimeout(reconnectTimer)
      }
      window.removeEventListener('resize', checkScreenSize)
      window.removeEventListener('beforeunload', saveCurrentSession)
    })

    // 从消息中提取来源数据
    const getSourceDataFromMessages = () => {
      const sourceData = []

      messages.value.forEach(message => {
        // 检查消息是否为结构化响应
        if (typeof message.text === 'object' && message.text?.type === 'structured_response') {
          const content = message.text.content || []
          content.forEach(element => {
            if (element.source_attribution) {
              sourceData.push(element)
            }
          })
        }
      })

      return sourceData
    }

    return {
      connectionStatus,
      currentView,
      showSettings,
      inputMessage,
      messages,
      messageList,
      backendUrl,
      autoReconnect,
      isTyping,
      isConnecting,
      isMobile,
      sidebarOpen,
      sessions,
      currentSessionId,
      showSessionList,
      // 🚀 新增：会话上下文管理
      showSessionContext,
      selectedSessionContext,
      selectedSessionId,
      searchQuery,
      showSearch,
      searchResults,
      messageTypeFilter,
      dateRangeFilter,
      filteredMessages,
      isListening,
      voiceStatus,
      voiceStatusType,
      voiceStatusIcon,
      // 工具推荐相关
      recommendedTools,
      queryAnalysis,
      showToolSelection,
      handleToolSelection,
      toggleConnection,
      handleMenuSelect,
      sendMessage,
      clearChat,
      saveSettings,
      formatTime,
      formatMessageText,
      formatSessionTime,
      scrollToBottom,
      toggleSidebar,
      closeSidebar,
      createNewSession,
      switchSession,
      deleteSession,
      // 🚀 新增：会话上下文管理方法
      continueSession,
      openSessionContext,
      loadSessionContext,
      continueSessionFromContext,
      performSearch,
      highlightSearchText,
      jumpToMessage,
      filterMessages,
      clearFilters,
      clearSearch,
      getMessageTypeLabel,
      getMessageTypeIcon,
      getMessageTypeClass,
      getProgressClass,
      getToolIcon,
      selectTool,
      toggleVoiceInput,
      // 重连相关
      reconnectionInfo,
      showReconnectDialog,
      manualReconnect,
      getConnectionStatusText,
      getConnectionStatusIcon,
      getSourceDataFromMessages,
      Menu,
      Delete,
      Search,
      Microphone,
      Close
    }
  }
}
</script>

<style scoped>
/* 全局变量定义 */
:root {
  --primary-color: #409EFF;
  --primary-light: #79bbff;
  --primary-dark: #337ecc;
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --info-color: #909399;

  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;

  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-light: #9ca3af;

  --border-color: #e5e7eb;
  --border-light: #f3f4f6;

  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
}

.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.app-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-light);
  padding: 0 24px;
  box-shadow: var(--shadow-sm);
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.app-title {
  font-size: 22px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.main-container {
  flex: 1;
  height: calc(100vh - 64px);
  overflow: hidden;
}

.app-sidebar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-right: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
}

.sidebar-menu {
  border: none;
  height: 100%;
  background: transparent;
}

.app-main {
  background: transparent;
  padding: 24px;
  overflow: hidden;
}

.chat-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-xl);
  padding: 24px;
  box-shadow: var(--shadow-lg);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 2px solid var(--border-light);
  background: linear-gradient(90deg, var(--bg-secondary), var(--bg-primary));
  border-radius: var(--radius-lg);
  padding: 20px 24px;
  margin: -24px -24px 24px -24px;
}

.chat-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 12px;
}

.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
  margin-bottom: 24px;
  scroll-behavior: smooth;
}

/* 自定义滚动条 */
.message-list::-webkit-scrollbar {
  width: 6px;
}

.message-list::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: 3px;
}

.message-list::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.message-list::-webkit-scrollbar-thumb:hover {
  background: var(--text-light);
}

.message-item {
  margin-bottom: 20px;
  display: flex;
  animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-item.user {
  justify-content: flex-end;
}

.message-item.user .message-content {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: #000000 !important; /* 强制使用黑色字体 */
  font-weight: 500; /* 增加字体粗细 */
  box-shadow: var(--shadow-md);
  position: relative;
}

.message-item.user .message-content::after {
  content: '';
  position: absolute;
  top: 50%;
  right: -8px;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border: 8px solid transparent;
  border-left-color: var(--primary-dark);
}

.message-item.ai .message-content {
  background: var(--bg-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
  position: relative;
}

.message-item.ai .message-content::after {
  content: '';
  position: absolute;
  top: 50%;
  left: -8px;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border: 8px solid transparent;
  border-right-color: var(--bg-primary);
}

.message-item.system .message-content {
  background: linear-gradient(135deg, #e0f2fe, #f0f9ff);
  color: var(--text-primary);
  border-left: 4px solid var(--primary-color);
  box-shadow: var(--shadow-sm);
}

.message-item.processing .message-content {
  background: linear-gradient(135deg, #fff8e1, #fff3cd);
  border-left: 4px solid var(--warning-color);
  animation: processingPulse 2s infinite;
}

@keyframes processingPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

.message-item.final .message-content {
  background: linear-gradient(135deg, #f0fff4, #e8f5e8);
  border-left: 4px solid var(--success-color);
  box-shadow: var(--shadow-sm);
}

.message-content {
  max-width: 75%;
  padding: 16px 20px;
  border-radius: var(--radius-xl);
  word-wrap: break-word;
  line-height: 1.6;
  font-size: 15px;
  transition: all 0.2s ease;
}

.message-content:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.message-text {
  margin-bottom: 8px;
  line-height: 1.6;
}

/* Markdown元素样式 */
.message-text h1, .message-text h2, .message-text h3 {
  margin: 16px 0 8px 0;
  font-weight: 600;
  color: var(--text-primary);
}

.message-text h1 {
  font-size: 1.5em;
  border-bottom: 2px solid var(--border-light);
  padding-bottom: 4px;
}

.message-text h2 {
  font-size: 1.3em;
  color: var(--primary-color);
}

.message-text h3 {
  font-size: 1.1em;
  color: var(--primary-dark);
}

.message-text hr {
  margin: 16px 0;
  border: none;
  border-top: 1px solid var(--border-light);
}

.message-text p {
  margin: 8px 0;
  line-height: 1.6;
}

.message-text ul, .message-text ol {
  margin: 8px 0;
  padding-left: 20px;
}

.message-text li {
  margin: 4px 0;
  line-height: 1.5;
}

.message-text table {
  width: 100%;
  border-collapse: collapse;
  margin: 12px 0;
  font-size: 14px;
}

.message-text th {
  padding: 10px 12px;
  border: 1px solid var(--border-light);
  background-color: var(--bg-tertiary);
  font-weight: 600;
  text-align: left;
  color: var(--text-primary);
}

.message-text td {
  padding: 8px 12px;
  border: 1px solid var(--border-light);
  text-align: left;
}

.message-text tr:nth-child(even) {
  background-color: var(--bg-secondary);
}

.message-text code {
  background: var(--bg-secondary);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 0.9em;
}

.message-text pre {
  background: var(--bg-secondary);
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 12px 0;
  border: 1px solid var(--border-light);
}

.message-text pre code {
  background: none;
  padding: 0;
  border-radius: 0;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 0.9em;
  line-height: 1.4;
}

/* 数学公式样式 */
.message-text .math-formula {
  display: block;
  text-align: center;
  margin: 16px 0;
  padding: 8px;
  background: var(--bg-secondary);
  border-radius: 6px;
  border: 1px solid var(--border-light);
}

.message-text .math-inline {
  display: inline;
  margin: 0 2px;
}

.message-text .math-fallback {
  background: var(--bg-secondary);
  padding: 8px 12px;
  border-radius: 4px;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 0.9em;
  color: var(--text-secondary);
  border: 1px solid var(--border-light);
  margin: 8px 0;
}

.message-text strong {
  font-weight: 600;
  color: var(--text-primary);
}

.message-time {
  font-size: 11px;
  opacity: 0.6;
  color: var(--text-light);
  font-weight: 500;
  margin-top: 4px;
}

.chat-input {
  border-top: 2px solid var(--border-light);
  padding-top: 20px;
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: 20px;
  margin: 0 -24px -24px -24px;
}

/* 语音输入样式 */
.input-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.voice-btn {
  flex-shrink: 0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.voice-btn.listening {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  border-color: #ff6b6b;
  color: white;
  box-shadow: 0 0 20px rgba(255, 107, 107, 0.4);
}

.voice-btn.pulse {
  animation: pulse-voice 1.5s ease-in-out infinite;
}

@keyframes pulse-voice {
  0% {
    transform: scale(1);
    box-shadow: 0 0 20px rgba(255, 107, 107, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 30px rgba(255, 107, 107, 0.6);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 20px rgba(255, 107, 107, 0.4);
  }
}

.text-input {
  flex: 1;
}

.voice-status {
  margin-top: 8px;
  padding: 8px 12px;
  border-radius: var(--radius-md);
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.voice-status.listening {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.1), rgba(103, 194, 58, 0.1));
  border: 1px solid rgba(64, 158, 255, 0.3);
  color: var(--primary-dark);
}

.voice-status.processing {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 152, 0, 0.1));
  border: 1px solid rgba(255, 193, 7, 0.3);
  color: #e65100;
}

.voice-status.success {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(139, 195, 74, 0.1));
  border: 1px solid rgba(76, 175, 80, 0.3);
  color: #2e7d32;
}

.voice-status.error {
  background: linear-gradient(135deg, rgba(244, 67, 54, 0.1), rgba(255, 87, 34, 0.1));
  border: 1px solid rgba(244, 67, 54, 0.3);
  color: #c62828;
}

.voice-status.info {
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.1), rgba(3, 169, 244, 0.1));
  border: 1px solid rgba(33, 150, 243, 0.3);
  color: #1565c0;
}

.status-icon {
  font-size: 16px;
  animation: status-pulse 2s ease-in-out infinite;
}

@keyframes status-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.status-text {
  font-weight: 500;
}

.placeholder-view {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
}

/* 增强的消息头部样式 */
.message-header-enhanced {
  margin-bottom: 12px;
  padding: 8px 0;
}

.message-type-indicator {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
}

.message-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-right: 12px;
  transition: all 0.3s ease;
}

.message-icon {
  font-size: 18px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  z-index: 2;
}

.message-type-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.message-type-label {
  font-size: 13px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.message-timestamp {
  font-size: 11px;
  color: var(--text-secondary);
  opacity: 0.8;
}

/* 处理中的旋转动画 */
.processing-spinner {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 进度条样式 */
.progress-bar {
  width: 100%;
  height: 3px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  overflow: hidden;
  margin-top: 4px;
}

.progress-fill {
  height: 100%;
  border-radius: 2px;
  animation: progress-flow 2s ease-in-out infinite;
  background: linear-gradient(90deg, transparent, currentColor, transparent);
  background-size: 200% 100%;
}

@keyframes progress-flow {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* 不同消息类型的样式 */
.type-user {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.type-ai {
  background: linear-gradient(135deg, #f093fb, #f5576c);
  color: white;
}

.type-system {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  color: white;
}

.type-processing {
  background: linear-gradient(135deg, #fa709a, #fee140);
  color: white;
}

.type-analysis {
  background: linear-gradient(135deg, #a8edea, #fed6e3);
  color: #333;
}

.type-tools {
  background: linear-gradient(135deg, #ffecd2, #fcb69f);
  color: #333;
}

.type-data {
  background: linear-gradient(135deg, #89f7fe, #66a6ff);
  color: white;
}

.type-ai-analysis {
  background: linear-gradient(135deg, #fdbb2d, #22c1c3);
  color: white;
}

.type-streaming {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  animation: pulse 2s infinite;
}

.type-final {
  background: linear-gradient(135deg, #56ab2f, #a8e6cf);
  color: white;
}

.type-default {
  background: linear-gradient(135deg, #e0e0e0, #f5f5f5);
  color: #333;
}

.type-error {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
  border-left: 4px solid #d32f2f;
}

.type-error .message-content {
  padding: 16px;
}

.type-error .error-description {
  margin-top: 8px;
  font-size: 14px;
  opacity: 0.9;
  line-height: 1.4;
}

.type-error .error-suggestions {
  margin-top: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.type-error .error-suggestions h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
}

.type-error .error-suggestions ul {
  margin: 0;
  padding-left: 16px;
  list-style-type: disc;
}

.type-error .error-suggestions li {
  margin: 4px 0;
  font-size: 13px;
  line-height: 1.3;
}

/* 进度条颜色 */
.progress-start { color: #fa709a; }
.progress-analysis { color: #a8edea; }
.progress-tools { color: #ffecd2; }
.progress-data { color: #89f7fe; }
.progress-ai { color: #fdbb2d; }
.progress-default { color: #409eff; }

/* 工具推荐交互式卡片样式 */
.tools-recommendation {
  margin: 16px 0;
  padding: 16px;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.05), rgba(103, 194, 58, 0.05));
  border: 1px solid rgba(64, 158, 255, 0.2);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
}

/* 增强的工具推荐区域 */
.enhanced-tools {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.1), rgba(103, 194, 58, 0.1));
  border: 2px solid rgba(64, 158, 255, 0.4);
  box-shadow: 0 8px 16px rgba(64, 158, 255, 0.2);
  animation: tools-glow 3s ease-in-out infinite alternate;
}

@keyframes tools-glow {
  0% {
    box-shadow: 0 8px 16px rgba(64, 158, 255, 0.2);
  }
  100% {
    box-shadow: 0 12px 24px rgba(64, 158, 255, 0.3);
  }
}

.tools-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(64, 158, 255, 0.1);
}

.tools-title {
  font-weight: 600;
  color: var(--primary-dark);
  font-size: 14px;
}

.tools-count {
  font-size: 12px;
  color: var(--text-secondary);
  background: rgba(64, 158, 255, 0.1);
  padding: 2px 8px;
  border-radius: var(--radius-sm);
}

/* 选择提示样式 */
.selection-prompt {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 12px 0;
  padding: 12px;
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 152, 0, 0.1));
  border: 2px dashed rgba(255, 193, 7, 0.5);
  border-radius: var(--radius-md);
  animation: prompt-pulse 2s ease-in-out infinite;
}

.prompt-icon {
  font-size: 24px;
  margin-right: 8px;
  animation: point-down 1.5s ease-in-out infinite;
}

.prompt-text {
  font-size: 16px;
  font-weight: bold;
  color: #e65100;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

@keyframes prompt-pulse {
  0%, 100% {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 152, 0, 0.1));
  }
  50% {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 152, 0, 0.2));
  }
}

@keyframes point-down {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(5px);
  }
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 12px;
}

.tool-card {
  display: flex;
  align-items: center;
  padding: 12px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(64, 158, 255, 0.15);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.tool-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  border-color: rgba(64, 158, 255, 0.3);
  background: rgba(255, 255, 255, 0.95);
}

/* 增强的工具卡片 */
.enhanced-card {
  background: rgba(255, 255, 255, 0.95);
  border: 2px solid rgba(64, 158, 255, 0.3);
  box-shadow: 0 6px 12px rgba(64, 158, 255, 0.2);
  transition: all 0.3s ease;
}

.enhanced-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 8px 20px rgba(64, 158, 255, 0.3);
  border-color: rgba(64, 158, 255, 0.5);
  background: rgba(255, 255, 255, 1);
}

.tool-icon {
  font-size: 24px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(64, 158, 255, 0.1);
  border-radius: var(--radius-md);
}

.tool-info {
  flex: 1;
  min-width: 0;
}

.tool-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 14px;
  margin-bottom: 4px;
}

.tool-description {
  font-size: 12px;
  color: var(--text-secondary);
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tool-action {
  margin-left: 12px;
}

/* 增强的选择按钮样式 */
.enhanced-select-button {
  position: relative;
  overflow: hidden;
  animation: button-pulse 2s ease-in-out infinite;
}

.enhanced-select-button:hover {
  transform: scale(1.05) !important;
  box-shadow: 0 6px 12px rgba(64, 158, 255, 0.4) !important;
  background-color: #337ecc !important;
}

.enhanced-select-button:active {
  transform: scale(0.98) !important;
}

.enhanced-select-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.enhanced-select-button:hover::before {
  left: 100%;
}

@keyframes button-pulse {
  0%, 100% {
    box-shadow: 0 4px 8px rgba(64, 158, 255, 0.3);
  }
  50% {
    box-shadow: 0 6px 12px rgba(64, 158, 255, 0.5);
  }
}

.message-text strong {
  font-weight: 700;
  color: var(--text-primary);
}

.message-text em {
  font-style: italic;
  color: var(--text-secondary);
}

.message-text code {
  background: var(--bg-tertiary);
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.9em;
  border: 1px solid var(--border-color);
  color: var(--primary-dark);
}

/* 连接状态指示器样式 */
.connection-badge {
  position: relative;
}

.connection-badge .el-badge__content {
  animation: connectionPulse 2s infinite;
}

@keyframes connectionPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* 打字指示器样式 */
.typing-indicator .message-content {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
  padding: 16px 20px;
}

.typing-animation {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 8px;
}

.typing-animation span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--primary-color);
  animation: typingBounce 1.4s infinite ease-in-out;
}

.typing-animation span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-animation span:nth-child(2) {
  animation-delay: -0.16s;
}

.typing-animation span:nth-child(3) {
  animation-delay: 0s;
}

@keyframes typingBounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.typing-text {
  font-size: 13px;
  color: var(--text-secondary);
  font-style: italic;
}

/* 移动端特定样式 */
.mobile-menu-btn {
  margin-right: 8px;
  color: var(--primary-color);
}

.sidebar-overlay {
  position: fixed;
  top: 64px;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  backdrop-filter: blur(2px);
}

/* 侧边栏动画 */
.app-sidebar {
  transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.app-sidebar.mobile-open {
  left: 0 !important;
}

/* 会话管理样式 */
.chat-header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.chat-header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.session-btn {
  color: var(--text-secondary);
  font-size: 13px;
}

.session-btn:hover {
  color: var(--primary-color);
}

.session-dialog .el-dialog__body {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

.session-dialog .el-dialog {
  margin: 0 auto;
  max-width: calc(100vw - 40px);
}

.session-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.session-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all 0.2s ease;
  background: var(--bg-primary);
}

.session-item:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-sm);
  transform: translateY(-1px);
}

.session-item.active {
  border-color: var(--primary-color);
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.05), rgba(64, 158, 255, 0.1));
  box-shadow: var(--shadow-md);
}

.session-content {
  flex: 1;
  min-width: 0;
}

.session-title {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.session-info {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: var(--text-secondary);
}

.session-time {
  font-weight: 500;
}

.session-count {
  padding: 2px 6px;
  background: var(--bg-tertiary);
  border-radius: var(--radius-sm);
}

.session-actions {
  margin-left: 12px;
  display: flex;
  gap: 8px;
}

.continue-btn {
  font-size: 12px;
}

.context-btn {
  font-size: 12px;
}

/* 🚀 新增：会话上下文相关样式 */
.session-context {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.1), rgba(64, 158, 255, 0.2));
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-size: 11px;
  color: var(--primary-color);
  font-weight: 500;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.session-insights {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.insight-tag {
  font-size: 10px;
  height: 20px;
  line-height: 18px;
}

/* 会话上下文对话框样式 */
.context-dialog .el-dialog__body {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.context-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.context-section {
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: 16px;
  background: var(--bg-primary);
}

.context-section h4 {
  margin: 0 0 12px 0;
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.context-info p {
  margin: 8px 0;
  color: var(--text-secondary);
  font-size: 14px;
}

.context-summary {
  background: var(--bg-secondary);
  padding: 12px;
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: 14px;
  line-height: 1.5;
  border-left: 4px solid var(--primary-color);
}

.context-entities {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.entity-tag {
  font-size: 12px;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.intelligent-insights {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.insight-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.insight-item strong {
  color: var(--text-primary);
  font-size: 14px;
}

.tool-tag {
  margin-right: 8px;
  margin-bottom: 4px;
}

.advice-text {
  color: var(--text-secondary);
  font-size: 13px;
  background: var(--bg-secondary);
  padding: 8px 12px;
  border-radius: var(--radius-md);
  border-left: 3px solid var(--warning-color);
}

.suggestions-list {
  margin: 0;
  padding-left: 20px;
  color: var(--text-secondary);
  font-size: 13px;
}

.suggestions-list li {
  margin-bottom: 4px;
}

.relevant-memories {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.memory-item {
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: 12px;
  background: var(--bg-secondary);
}

.memory-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.memory-id {
  font-family: monospace;
  font-size: 11px;
  color: var(--text-tertiary);
  background: var(--bg-tertiary);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
}

.memory-content p {
  margin: 6px 0;
  font-size: 13px;
  line-height: 1.4;
}

.memory-content strong {
  color: var(--text-primary);
}

.memory-tags {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.context-loading {
  text-align: center;
  padding: 40px 20px;
}

.empty-sessions {
  text-align: center;
  padding: 40px 20px;
}

/* 搜索功能样式 */
.search-dialog .el-dialog__body {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.search-dialog .el-dialog {
  margin: 0 auto;
  max-width: calc(100vw - 40px);
}

.search-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
  box-sizing: border-box;
}

.search-input-section {
  margin-bottom: 16px;
  width: 100%;
}

.search-input-section .el-input {
  width: 100%;
}

.filter-section {
  padding: 16px;
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  width: 100%;
  box-sizing: border-box;
}

.filter-section .el-row {
  margin-bottom: 12px;
}

.filter-section .el-col {
  padding-right: 8px;
}

.filter-section .el-col:last-child {
  padding-right: 0;
}

.search-results {
  max-height: 400px;
  overflow-y: auto;
}

.results-header {
  padding: 12px 0;
  border-bottom: 1px solid var(--border-light);
  margin-bottom: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.result-item {
  padding: 16px;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all 0.2s ease;
  background: var(--bg-primary);
}

.result-item:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-sm);
  transform: translateY(-1px);
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.result-text {
  color: var(--text-primary);
  line-height: 1.5;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.result-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: var(--text-secondary);
}

.result-session {
  padding: 2px 6px;
  background: var(--primary-color);
  color: white;
  border-radius: var(--radius-sm);
  font-weight: 500;
}

.result-type {
  padding: 2px 6px;
  background: var(--bg-tertiary);
  border-radius: var(--radius-sm);
}

.result-time {
  font-weight: 500;
}

.search-highlight {
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  padding: 2px 4px;
  border-radius: 3px;
  font-weight: 600;
  color: var(--warning-color);
}

.message-highlight {
  animation: messageHighlight 3s ease-out;
}

@keyframes messageHighlight {
  0% {
    background: rgba(255, 235, 59, 0.3);
    transform: scale(1.02);
  }
  100% {
    background: transparent;
    transform: scale(1);
  }
}

.no-results {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-secondary);
}

/* 响应式设计 - 移动优先 */

/* 平板设备 (768px - 1024px) */
@media (max-width: 1024px) and (min-width: 769px) {
  .app-sidebar {
    width: 200px !important;
  }

  .app-main {
    padding: 20px;
  }

  .message-content {
    max-width: 80%;
  }
}

/* 小平板和大手机 (481px - 768px) */
@media (max-width: 768px) and (min-width: 481px) {
  .app-container {
    background: var(--bg-primary);
  }

  .main-container {
    position: relative;
  }

  .app-sidebar {
    position: fixed;
    top: 64px;
    left: -250px;
    width: 250px !important;
    height: calc(100vh - 64px);
    z-index: 1000;
    transition: left 0.3s ease;
    box-shadow: var(--shadow-lg);
  }

  .app-sidebar.mobile-open {
    left: 0;
  }

  .app-main {
    padding: 16px;
    width: 100%;
  }

  .chat-container {
    border-radius: var(--radius-lg);
    padding: 16px;
    height: calc(100vh - 96px);
  }

  .message-content {
    max-width: 85%;
    padding: 12px 16px;
  }

  .app-title {
    font-size: 18px;
  }

  .header-content {
    padding: 0 16px;
  }

  .chat-header {
    margin: -16px -16px 20px -16px;
    padding: 16px 20px;
  }

  .chat-input {
    margin: 0 -16px -16px -16px;
    padding: 16px;
  }
}

/* 手机设备 (320px - 480px) */
@media (max-width: 480px) {
  .app-header {
    padding: 0 12px;
  }

  .header-content {
    gap: 8px;
  }

  .logo-section {
    gap: 8px;
  }

  .app-title {
    font-size: 16px;
    font-weight: 600;
  }

  .header-actions {
    gap: 8px;
  }

  .header-actions .el-button {
    padding: 8px 12px;
    font-size: 12px;
  }

  .app-sidebar {
    width: 280px !important;
    left: -280px;
  }

  .app-main {
    padding: 12px;
  }

  .chat-container {
    padding: 12px;
    border-radius: var(--radius-md);
    height: calc(100vh - 88px);
  }

  .chat-header {
    margin: -12px -12px 16px -12px;
    padding: 12px 16px;
  }

  .chat-header h2 {
    font-size: 18px;
  }

  .message-content {
    max-width: 90%;
    padding: 10px 14px;
    font-size: 14px;
    border-radius: var(--radius-md);
  }

  .message-text {
    line-height: 1.5;
  }

  .message-time {
    font-size: 10px;
  }

  .chat-input {
    margin: 0 -12px -12px -12px;
    padding: 12px;
  }

  /* 移动端工具推荐样式 */
  .tools-recommendation {
    margin: 12px 0;
    padding: 12px;
  }

  .tools-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .tool-card {
    padding: 10px;
  }

  .tool-icon {
    font-size: 20px;
    width: 32px;
    height: 32px;
    margin-right: 10px;
  }

  .tool-name {
    font-size: 13px;
  }

  .tool-description {
    font-size: 11px;
  }

  .tool-action .el-button {
    padding: 4px 8px;
    font-size: 12px;
  }

  /* 移动端消息头部优化 */
  .message-icon-wrapper {
    width: 32px;
    height: 32px;
    margin-right: 10px;
  }

  .message-icon {
    font-size: 16px;
  }

  .message-type-label {
    font-size: 12px;
  }

  .message-timestamp {
    font-size: 10px;
  }

  .progress-bar {
    height: 2px;
  }

  /* 移动端语音输入优化 */
  .input-container {
    gap: 8px;
  }

  .voice-btn {
    width: 44px;
    height: 44px;
  }

  .voice-status {
    margin-top: 6px;
    padding: 6px 10px;
    font-size: 12px;
  }

  .status-icon {
    font-size: 14px;
  }

  /* 搜索对话框移动端优化 */
  .search-dialog .el-dialog {
    width: 95vw !important;
    margin: 20px auto;
    max-width: none;
  }

  .search-dialog .el-dialog__body {
    padding: 16px;
  }

  .filter-section {
    padding: 12px;
  }

  .filter-section .el-row {
    flex-direction: column;
    gap: 12px;
  }

  .filter-section .el-col {
    width: 100% !important;
    padding-right: 0;
    margin-bottom: 8px;
  }

  .search-results {
    max-height: 300px;
  }

  .result-item {
    padding: 12px;
  }

  .result-text {
    font-size: 14px;
    -webkit-line-clamp: 2;
  }

  .result-meta {
    font-size: 11px;
    flex-wrap: wrap;
    gap: 8px;
  }

  /* 会话对话框移动端优化 */
  .session-dialog .el-dialog {
    width: 95vw !important;
    margin: 20px auto;
    max-width: none;
  }

  .session-dialog .el-dialog__body {
    padding: 16px;
    max-height: 50vh;
  }

  .session-item {
    padding: 12px;
  }

  .session-title {
    font-size: 14px;
  }

  .session-info {
    font-size: 11px;
  }
}

/* 超小屏幕 (< 320px) */
@media (max-width: 319px) {
  .app-title {
    display: none;
  }

  .message-content {
    max-width: 95%;
    padding: 8px 12px;
    font-size: 13px;
  }

  .chat-container {
    padding: 8px;
  }

  .chat-header {
    margin: -8px -8px 12px -8px;
    padding: 8px 12px;
  }

  .chat-input {
    margin: 0 -8px -8px -8px;
    padding: 8px;
  }
}

/* 横屏模式优化 */
@media (max-height: 500px) and (orientation: landscape) {
  .chat-container {
    height: calc(100vh - 80px);
  }

  .chat-header {
    padding: 12px 20px;
    margin-bottom: 16px;
  }

  .message-list {
    padding: 8px 0;
  }

  .chat-input {
    padding: 12px 20px;
  }
}

/* 高分辨率屏幕优化 */
@media (min-width: 1440px) {
  .app-main {
    padding: 32px;
  }

  .chat-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 32px;
  }

  .message-content {
    max-width: 65%;
    padding: 18px 24px;
    font-size: 16px;
  }

  .chat-header h2 {
    font-size: 28px;
  }
}

/* 打印样式 */
@media print {
  .app-header,
  .app-sidebar,
  .chat-input,
  .header-actions {
    display: none !important;
  }

  .app-container {
    background: white !important;
  }

  .chat-container {
    box-shadow: none !important;
    border: 1px solid #ccc;
  }

  .message-content {
    max-width: 100% !important;
    box-shadow: none !important;
    border: 1px solid #ddd;
    margin-bottom: 10px;
  }
}

/* 重连对话框样式 */
.reconnect-dialog {
  text-align: center;
  padding: 20px 0;
}

.reconnect-icon {
  margin-bottom: 20px;
}

.reconnect-content h3 {
  color: #303133;
  margin: 0 0 15px 0;
  font-size: 18px;
  font-weight: 600;
}

.reconnect-content p {
  color: #606266;
  margin: 0 0 20px 0;
  line-height: 1.6;
}

.reconnect-info {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin: 20px 0;
  text-align: left;
}

.reconnect-info p {
  margin: 0 0 10px 0;
  font-weight: 600;
  color: #303133;
}

.reconnect-info ul {
  margin: 0;
  padding-left: 20px;
  color: #606266;
}

.reconnect-info li {
  margin: 5px 0;
  line-height: 1.5;
}

.reconnect-progress {
  margin-top: 20px;
}

.progress-text {
  display: block;
  margin-top: 10px;
  color: #909399;
  font-size: 14px;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 15px;
}
</style>
