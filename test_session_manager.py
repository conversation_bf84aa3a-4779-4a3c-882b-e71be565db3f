#!/usr/bin/env python3
"""
测试SessionManager的智能上下文管理功能
"""

import asyncio
import sys
import os

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'web_turbine_interface/turbine_backend/app/services'))

from session_manager import SessionManager

async def test_session_manager():
    """测试会话管理器"""
    print("🧪 开始测试SessionManager...")
    
    # 创建会话管理器
    session_manager = SessionManager()
    
    # 测试会话创建
    session_id = "test_session_001"
    session = await session_manager.create_session(session_id)
    print(f"✅ 创建会话: {session.session_id}")
    
    # 测试添加消息
    await session_manager.add_message(
        session_id=session_id,
        user_query="风机齿轮箱温度异常，如何诊断？",
        ai_response="齿轮箱温度异常通常由以下原因引起：1. 润滑油不足或老化 2. 轴承磨损 3. 负载过大。建议检查润滑系统和轴承状态。",
        tool_results={"wind-turbine-db": {"success": True, "data": "温度数据"}},
        source_attribution={"tool_sources": ["wind-turbine-db"]}
    )
    print("✅ 添加第一条消息")
    
    # 测试获取上下文
    context = await session_manager.get_conversation_context(
        session_id, 
        current_query="齿轮箱维护周期是多久？"
    )
    print(f"✅ 获取上下文: 历史消息数={len(context['recent_messages'])}, 相关记忆数={len(context.get('relevant_memories', []))}")
    
    # 添加第二条消息
    await session_manager.add_message(
        session_id=session_id,
        user_query="齿轮箱维护周期是多久？",
        ai_response="齿轮箱的维护周期通常为：1. 日常检查：每天 2. 润滑油更换：6个月 3. 大修：2-3年。具体周期需根据运行环境和负载情况调整。",
        tool_results={"local-knowledge-base": {"success": True, "data": "维护手册"}},
        source_attribution={"tool_sources": ["local-knowledge-base"]}
    )
    print("✅ 添加第二条消息")
    
    # 再次获取上下文
    context = await session_manager.get_conversation_context(
        session_id,
        current_query="如何判断齿轮箱是否需要大修？"
    )
    print(f"✅ 更新后上下文: 历史消息数={len(context['recent_messages'])}")
    print(f"   上下文摘要: {context['context_summary']}")
    print(f"   关键实体: {context['key_entities']}")
    
    # 测试会话统计
    stats = session_manager.get_session_stats()
    print(f"✅ 会话统计: {stats}")
    
    print("🎉 SessionManager测试完成！")

if __name__ == "__main__":
    asyncio.run(test_session_manager())
