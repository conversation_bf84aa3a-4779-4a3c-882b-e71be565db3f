#!/usr/bin/env python3
"""
测试导入路径调试脚本
用于验证LangGraph智能体的导入是否正确
"""

import sys
import os

def test_import_paths():
    """测试导入路径"""
    print("🔧 测试导入路径...")
    
    # 添加项目根目录到Python路径
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__)))
    src_path = os.path.join(project_root, 'src')
    
    print(f"📁 项目根目录: {project_root}")
    print(f"📁 src路径: {src_path}")
    print(f"📁 src路径存在: {os.path.exists(src_path)}")
    
    if src_path not in sys.path:
        sys.path.insert(0, src_path)
        print(f"✅ 已添加src路径到Python路径")
    
    # 测试导入
    try:
        print("🔄 尝试导入InteractiveLangGraphAgent...")
        from interactive_langgraph_agent import InteractiveLangGraphAgent
        print("✅ InteractiveLangGraphAgent导入成功")
        
        print("🔄 尝试导入config...")
        from config import get_config
        print("✅ config导入成功")
        
        print("🔄 尝试初始化配置...")
        config = get_config()
        print(f"✅ 配置初始化成功: {type(config)}")
        
        print("🔄 尝试创建智能体实例...")
        agent = InteractiveLangGraphAgent(config)
        print(f"✅ 智能体实例创建成功: {type(agent)}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_multi_api_client():
    """测试MultiAPIClient"""
    try:
        print("🔄 尝试导入MultiAPIClient...")
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
        from multi_api_client import MultiAPIClient
        print("✅ MultiAPIClient导入成功")
        
        print("🔄 尝试导入config...")
        from config import get_config
        config = get_config()
        
        print("🔄 尝试创建MultiAPIClient实例...")
        client = MultiAPIClient(config)
        print(f"✅ MultiAPIClient实例创建成功: {type(client)}")
        
        return True
        
    except Exception as e:
        print(f"❌ MultiAPIClient测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始导入路径调试测试...")
    
    # 测试导入路径
    import_success = test_import_paths()
    
    # 测试MultiAPIClient
    api_success = test_multi_api_client()
    
    print("\n📊 测试结果:")
    print(f"   导入测试: {'✅ 成功' if import_success else '❌ 失败'}")
    print(f"   API测试: {'✅ 成功' if api_success else '❌ 失败'}")
    
    if import_success and api_success:
        print("🎉 所有测试通过！")
    else:
        print("⚠️ 存在问题需要修复")
