#!/usr/bin/env python3
"""
测试后端服务初始化脚本
用于验证LangGraph适配器的初始化是否正确
"""

import sys
import os
import asyncio

# 添加后端路径
backend_path = os.path.join(os.path.dirname(__file__), 'web_turbine_interface', 'turbine_backend')
sys.path.insert(0, backend_path)

async def test_langgraph_adapter():
    """测试LangGraph适配器初始化"""
    try:
        print("🔧 测试LangGraph适配器初始化...")
        
        # 导入适配器
        from app.services.langgraph_websocket_adapter import LangGraphWebSocketAdapter
        print("✅ LangGraphWebSocketAdapter导入成功")
        
        # 创建适配器实例
        adapter = LangGraphWebSocketAdapter()
        print("✅ 适配器实例创建成功")
        
        # 测试初始化
        print("🔄 尝试初始化适配器...")
        success = await adapter.initialize()
        
        if success:
            print("✅ 适配器初始化成功")
            print(f"   智能体类型: {type(adapter.agent)}")
            return True
        else:
            print("❌ 适配器初始化失败")
            if hasattr(adapter, 'initialization_error'):
                print(f"   错误信息: {adapter.initialization_error}")
            return False
            
    except Exception as e:
        print(f"❌ 适配器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_agent_service():
    """测试智能体服务初始化"""
    try:
        print("\n🔧 测试智能体服务初始化...")
        
        # 导入服务
        from app.services.agent_service import AgentService
        print("✅ AgentService导入成功")
        
        # 创建服务实例
        service = AgentService()
        print("✅ 服务实例创建成功")
        
        # 测试初始化
        print("🔄 尝试初始化服务...")
        success = await service.initialize()
        
        if success:
            print("✅ 服务初始化成功")
            print(f"   服务模式: {service.mode}")
            print(f"   初始化状态: {service.is_initialized}")
            return True
        else:
            print("❌ 服务初始化失败")
            if hasattr(service, 'initialization_error'):
                print(f"   错误信息: {service.initialization_error}")
            return False
            
    except Exception as e:
        print(f"❌ 服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 开始后端服务初始化测试...")
    
    # 测试LangGraph适配器
    adapter_success = await test_langgraph_adapter()
    
    # 测试智能体服务
    service_success = await test_agent_service()
    
    print("\n📊 测试结果:")
    print(f"   适配器测试: {'✅ 成功' if adapter_success else '❌ 失败'}")
    print(f"   服务测试: {'✅ 成功' if service_success else '❌ 失败'}")
    
    if adapter_success and service_success:
        print("🎉 所有测试通过！")
    else:
        print("⚠️ 存在问题需要修复")

if __name__ == "__main__":
    asyncio.run(main())
