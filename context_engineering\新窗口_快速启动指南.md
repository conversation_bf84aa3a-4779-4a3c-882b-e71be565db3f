# 新窗口快速启动指南 🚀

## 🎯 30秒了解项目状态

**✅ 当前成果**: C5阶段完成 - 数学公式重复渲染修复完成 (2025-07-30)
**🚨 当前状态**: 技术债务清理阶段，发现关键基础设施问题
**🎯 紧急任务**: 修复SessionManager异步初始化和模块导入路径问题
**⚠️ 重要**: 解决AD_HOC_TASKS.md中的技术债务后再进行后续开发

## 📋 新窗口必读文件清单 (5分钟快速上手)

### 🔥 第一优先级 (必须阅读)
1. **`AD_HOC_TASKS.md`** - 🚨 **技术债务清单** - SessionManager异步初始化和模块导入路径问题
2. **`C5数学公式重复渲染修复_完成总结.md`** - � **C5完成记录** - 数学公式修复完成总结
3. **`C4AI回答来源标注与上下文管理任务_完成总结.md`** - 📊 **C4完成记录** - 源标注与上下文管理完成总结
4. **`CONTEXT_PERFORMANCE_ANALYSIS_REPORT.md`** - 📊 **性能分析报告** - 系统性能基准和技术债务识别
5. **`CONTEXT_QUICK.md`** - 30秒了解项目全貌

### 📚 第二优先级 (强烈建议)
6. **`C2-002_范式转移架构实施_完成总结.md`** - 前置架构成果和技术突破
6. **`C2-001_前端Markdown渲染修复_完成总结.md`** - Markdown渲染修复成果
7. **`C1工具生态系统完善_完成总结.md`** - 历史任务成果和经验

## 🔧 立即可用的系统

### 验证系统正常
```bash
# 1. 环境激活
cd /e/风机智能体测试/local_hybrid_agent
conda activate viz

# 2. 测试命令行版本
python demo_interactive_agent.py
# 输入: 风机有哪些主要部件
# 预期: 显示工具推荐 → 用户选择 → AI专业回答

# 3. 启动Web版本
# 后端: cd web_turbine_interface/turbine_backend && python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
# 前端: cd web_turbine_interface/turbine_frontend && npm run dev
# 访问: http://localhost:3000 (注意端口是3000)
```

### ✅ 数学公式修复验证流程 (已完成)
1. 发送查询："请生成一个包含数学公式的测试，比如 $E = mc^2$ 和 $$\eta = \frac{P_{out}}{P_{in}}$$"
2. 观察AI回答中的数学公式显示
3. **修复结果**: ✅ 每个数学公式只显示一次，KaTeX渲染完美
4. **技术成就**: 后端ContentStructureParser文本清理机制完善
5. **验证状态**: ✅ Playwright MCP端到端测试通过

## 📋 当前紧急任务：技术债务清理

### 🎯 核心目标
解决基础设施问题，确保系统稳定性，为后续功能开发奠定坚实基础。

### 🔧 已识别的技术债务
1. **SessionManager异步初始化问题** - RuntimeError: no running event loop
2. **模块导入路径混乱** - HybridSemanticAnalyzer、IntelligentMemoryManager无法加载
3. **系统启动稳定性** - 异步初始化逻辑不统一

### 📊 实施步骤
1. **问题分析**: 深入分析SessionManager异步初始化冲突
2. **路径统一**: 统一sys.path管理，确保模块正确导入
3. **启动优化**: 实现延迟初始化模式，避免事件循环冲突
4. **测试验证**: 确保后端能正常启动并稳定运行

### 🎯 预期效果
- ✅ 后端启动无异步初始化错误
- ✅ 所有核心模块正确加载
- ✅ 系统启动稳定可靠
- ✅ 为后续开发提供稳定基础

## 🔍 关键技术背景

### 刚刚完成的核心突破
**成就1**: 数学公式重复渲染修复完成 (2025-07-30)
**技术**: ContentStructureParser文本清理机制，空格替换策略保持位置一致性
**成就2**: 上下文管理系统性能分析完成 (2025-07-30)
**技术**: 多维度性能测试，5.3s总响应时间，智能路由0.001s响应
**成就3**: AI回答来源标注系统完全实现 (2025-07-30)
**技术**: SourceAttribution数据结构和前端交互式源查看组件
**成就4**: 会话上下文管理系统完全实现 (2025-07-30)
**技术**: IntelligentMemoryManager和AdaptiveSemanticRouter智能上下文系统

### 当前架构优势
- ✅ 范式转移架构，后端结构化解析+前端组件化渲染
- ✅ 完整的手动工具选择功能，支持6个工具任意组合
- ✅ Human-in-the-Loop工作流程完全正常
- ✅ 6个MCP工具全部集成并正常工作
- ✅ 表格、数学公式、混合内容完美渲染
- ✅ Vue.js错误已修复，渐进式渲染正常工作

## 📁 关键文件位置

```
web_turbine_interface/
├── turbine_frontend/src/App.vue          # 前端主文件
├── turbine_backend/app/main.py           # 后端WebSocket服务
└── turbine_backend/app/services/
    └── langgraph_websocket_adapter.py    # 智能体WebSocket适配器

src/agents/InteractiveLangGraphAgent.py   # 智能体核心逻辑
demo_interactive_agent.py                 # 命令行版本（参考标准）
```

## 🚨 重要提醒

### 保持的技术原则
1. **不破坏现有功能** - 所有新功能都是增强，不是替换
2. **异步架构不变** - 保持当前的异步任务处理模式
3. **测试驱动开发** - 每个新功能都要验证完整工作流程

### 调试技巧
- **前端调试**: 浏览器Console查看WebSocket消息日志
- **后端调试**: 查看终端的logger.info输出
- **对比验证**: 命令行版本 vs Web版本行为对比

## 🎯 成功指标

### 用户体验目标
- 响应时间: <15秒（当前~30秒）
- 流式显示: AI回答逐字出现
- 进度可见: 每个执行步骤都有明确提示

### 技术指标
- 并发支持: 5个用户同时使用
- 稳定性: 24小时连续运行
- 错误恢复: 所有异常都能优雅处理

---

**🚨 立即行动**: 技术债务清理，解决SessionManager异步初始化和模块导入路径问题！

**详细指导**: 查看 `context_engineering/AD_HOC_TASKS.md` 中的技术债务清单

**⚠️ 重要提醒**:
- 前端服务: http://localhost:3000，后端服务: http://localhost:8000
- 使用Playwright MCP做最终实验验证
- 阶段性行动前利用Context7 MCP以及联网找寻最佳技术实践
- 一切以前端服务的测试效果为主，需要做TDD实验
- **强调将AD_HOC_TASKS.md中的技术债务解决后再进行后续开发**

## 🎯 上下文工程信息

### 项目背景
**风机智能体测试系统** - 混合AI智能体，支持命令行和Web两种版本
- **核心特性**: 6个MCP工具集成，结构化内容解析，实时WebSocket通信
- **技术架构**: 范式转移架构 - 后端结构化解析 + 前端组件化渲染
- **开发阶段**: 已完成C0-C5阶段，当前处于技术债务清理阶段

### 技术栈
- **后端**: Python + FastAPI + WebSocket + LangGraph
- **前端**: Vue.js 3 + Composition API + KaTeX + marked.js
- **测试**: Playwright MCP + TDD单元测试
- **工具**: 6个MCP工具 (local-knowledge-base, wind-turbine-db等)

### 关键成就
- ✅ 数学公式重复渲染修复 (C5)
- ✅ 上下文管理系统性能分析 (C4)
- ✅ AI回答来源标注系统 (C4)
- ✅ 范式转移架构实施 (C2)
- ✅ 工具生态系统完善 (C1)
- ✅ 智能工具推荐增强 (C0)
