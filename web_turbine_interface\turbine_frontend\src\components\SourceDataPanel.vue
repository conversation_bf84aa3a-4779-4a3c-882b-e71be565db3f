<template>
  <div class="source-data-panel">
    <!-- 面板触发按钮 -->
    <button 
      class="panel-trigger"
      @click="togglePanel"
      :class="{ 'active': isPanelOpen }"
      title="展开数据来源"
    >
      <span class="trigger-icon">📊</span>
      <span class="trigger-text">数据来源</span>
      <span class="source-count">{{ totalSources }}</span>
    </button>

    <!-- 侧边面板 -->
    <div 
      class="side-panel"
      :class="{ 'open': isPanelOpen }"
    >
      <!-- 面板头部 -->
      <div class="panel-header">
        <h3>数据来源详情</h3>
        <button class="close-btn" @click="closePanel">×</button>
      </div>

      <!-- 面板内容 -->
      <div class="panel-content">
        <!-- 来源统计概览 -->
        <div class="source-overview">
          <div class="overview-item">
            <span class="overview-label">总来源数</span>
            <span class="overview-value">{{ totalSources }}</span>
          </div>
          <div class="overview-item">
            <span class="overview-label">工具数据</span>
            <span class="overview-value">{{ toolSourceCount }}</span>
          </div>
          <div class="overview-item">
            <span class="overview-label">AI生成</span>
            <span class="overview-value">{{ aiSourceCount }}</span>
          </div>
        </div>

        <!-- 工具贡献列表 -->
        <div class="tool-contributions">
          <h4>工具贡献详情</h4>
          <div 
            v-for="(contribution, index) in toolContributions"
            :key="index"
            class="contribution-item"
            @click="toggleContributionDetails(index)"
          >
            <div class="contribution-header">
              <span class="tool-icon">{{ contribution.icon }}</span>
              <span class="tool-name">{{ contribution.name }}</span>
              <span class="contribution-count">{{ contribution.elementCount }} 项</span>
              <span class="expand-icon" :class="{ 'expanded': contribution.expanded }">▼</span>
            </div>
            
            <!-- 贡献详情 -->
            <div v-if="contribution.expanded" class="contribution-details">
              <div class="contribution-summary">
                <div class="summary-item">
                  <strong>置信度:</strong> {{ Math.round(contribution.avgConfidence * 100) }}%
                </div>
                <div class="summary-item">
                  <strong>内容类型:</strong> {{ contribution.elementTypes.join(', ') }}
                </div>
              </div>

              <!-- 具体贡献元素 -->
              <div class="contributed-elements">
                <div 
                  v-for="(element, elemIndex) in contribution.elements"
                  :key="elemIndex"
                  class="element-item"
                >
                  <div class="element-header">
                    <span class="element-type">{{ getElementTypeText(element.type) }}</span>
                    <span class="element-confidence" :class="getConfidenceClass(element.confidence)">
                      {{ Math.round(element.confidence * 100) }}%
                    </span>
                  </div>
                  <div class="element-preview">
                    {{ getElementPreview(element) }}
                  </div>
                </div>
              </div>

              <!-- 原始数据查看 -->
              <div class="raw-data-section">
                <button 
                  class="raw-data-btn"
                  @click="showRawData(contribution)"
                >
                  查看原始数据
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- AI生成内容统计 -->
        <div v-if="aiSourceCount > 0" class="ai-content-section">
          <h4>AI生成内容</h4>
          <div class="ai-stats">
            <div class="stat-item">
              <span class="stat-label">生成元素:</span>
              <span class="stat-value">{{ aiSourceCount }} 项</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">平均置信度:</span>
              <span class="stat-value">{{ Math.round(avgAiConfidence * 100) }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 原始数据弹窗 -->
    <div v-if="showRawDataModal" class="raw-data-modal" @click.self="hideRawData">
      <div class="modal-content">
        <div class="modal-header">
          <h3>{{ selectedTool?.name }} - 原始数据</h3>
          <button class="close-btn" @click="hideRawData">×</button>
        </div>
        <div class="modal-body">
          <pre class="raw-data-content">{{ formatRawData(selectedTool?.rawData) }}</pre>
        </div>
      </div>
    </div>

    <!-- 遮罩层 -->
    <div 
      v-if="isPanelOpen" 
      class="panel-overlay"
      @click="closePanel"
    ></div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'

export default {
  name: 'SourceDataPanel',
  props: {
    sourceData: {
      type: Array,
      default: () => []
    }
  },
  setup(props) {
    const isPanelOpen = ref(false)
    const showRawDataModal = ref(false)
    const selectedTool = ref(null)

    // 计算属性
    const totalSources = computed(() => {
      return props.sourceData.reduce((count, element) => {
        if (element.source_attribution) {
          return count + element.source_attribution.all_sources.length
        }
        return count
      }, 0)
    })

    const toolSourceCount = computed(() => {
      return props.sourceData.filter(element => 
        element.source_attribution?.primary_source?.source_type === 'tool'
      ).length
    })

    const aiSourceCount = computed(() => {
      return props.sourceData.filter(element => 
        element.source_attribution?.primary_source?.source_type === 'ai'
      ).length
    })

    const avgAiConfidence = computed(() => {
      const aiElements = props.sourceData.filter(element => 
        element.source_attribution?.primary_source?.source_type === 'ai'
      )
      if (aiElements.length === 0) return 0
      
      const totalConfidence = aiElements.reduce((sum, element) => 
        sum + element.source_attribution.primary_source.confidence, 0
      )
      return totalConfidence / aiElements.length
    })

    const toolContributions = computed(() => {
      const contributions = {}
      
      props.sourceData.forEach(element => {
        if (!element.source_attribution) return
        
        element.source_attribution.all_sources.forEach(source => {
          if (source.source_type === 'tool') {
            if (!contributions[source.tool_name]) {
              contributions[source.tool_name] = {
                name: getToolName(source.tool_name),
                icon: source.tool_icon,
                elements: [],
                elementCount: 0,
                avgConfidence: 0,
                elementTypes: new Set(),
                expanded: false,
                rawData: null
              }
            }
            
            contributions[source.tool_name].elements.push({
              type: element.type,
              content: element.content || element.headers || element.formula || element.text,
              confidence: source.confidence
            })
            contributions[source.tool_name].elementCount++
            contributions[source.tool_name].elementTypes.add(element.type)
          }
        })
      })
      
      // 计算平均置信度和转换Set为Array
      Object.values(contributions).forEach(contribution => {
        const totalConfidence = contribution.elements.reduce((sum, elem) => sum + elem.confidence, 0)
        contribution.avgConfidence = totalConfidence / contribution.elements.length
        contribution.elementTypes = Array.from(contribution.elementTypes)
      })
      
      return Object.values(contributions)
    })

    // 方法
    const togglePanel = () => {
      isPanelOpen.value = !isPanelOpen.value
    }

    const closePanel = () => {
      isPanelOpen.value = false
    }

    const toggleContributionDetails = (index) => {
      toolContributions.value[index].expanded = !toolContributions.value[index].expanded
    }

    const showRawData = (contribution) => {
      selectedTool.value = contribution
      showRawDataModal.value = true
    }

    const hideRawData = () => {
      showRawDataModal.value = false
      selectedTool.value = null
    }

    const getToolName = (toolName) => {
      const nameMap = {
        'local-knowledge-base': '本地知识库',
        'wind-turbine-db': '风机数据库',
        'context7': '技术文档库',
        'fetch': '在线信息',
        'pdf-processor': 'PDF文档',
        'filesystem': '文件系统'
      }
      return nameMap[toolName] || toolName
    }

    const getElementTypeText = (type) => {
      const typeMap = {
        'paragraph': '段落',
        'table': '表格',
        'math': '数学公式',
        'code': '代码块',
        'heading': '标题',
        'list': '列表'
      }
      return typeMap[type] || type
    }

    const getConfidenceClass = (confidence) => {
      if (confidence >= 0.8) return 'confidence-high'
      if (confidence >= 0.6) return 'confidence-medium'
      return 'confidence-low'
    }

    const getElementPreview = (element) => {
      const content = element.content
      if (typeof content === 'string') {
        return content.length > 100 ? content.substring(0, 100) + '...' : content
      }
      return `${element.type} 内容`
    }

    const formatRawData = (rawData) => {
      if (!rawData) return '暂无原始数据'
      return JSON.stringify(rawData, null, 2)
    }

    return {
      isPanelOpen,
      showRawDataModal,
      selectedTool,
      totalSources,
      toolSourceCount,
      aiSourceCount,
      avgAiConfidence,
      toolContributions,
      togglePanel,
      closePanel,
      toggleContributionDetails,
      showRawData,
      hideRawData,
      getElementTypeText,
      getConfidenceClass,
      getElementPreview,
      formatRawData
    }
  }
}
</script>

<style scoped>
/* 面板触发按钮 */
.panel-trigger {
  position: fixed;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  background: #2196f3;
  color: white;
  border: none;
  padding: 12px 8px;
  border-radius: 8px 0 0 8px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  font-size: 0.8em;
  box-shadow: -2px 0 8px rgba(0,0,0,0.2);
  transition: all 0.3s ease;
  z-index: 1000;
}

.panel-trigger:hover {
  background: #1976d2;
  transform: translateY(-50%) translateX(-4px);
}

.panel-trigger.active {
  background: #1976d2;
  transform: translateY(-50%) translateX(-4px);
}

.trigger-icon {
  font-size: 1.2em;
}

.trigger-text {
  font-size: 0.7em;
  writing-mode: vertical-rl;
  text-orientation: mixed;
}

.source-count {
  background: rgba(255,255,255,0.2);
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 0.7em;
  min-width: 16px;
  text-align: center;
}

/* 侧边面板 */
.side-panel {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100vh;
  background: white;
  box-shadow: -4px 0 12px rgba(0,0,0,0.15);
  transition: right 0.3s ease;
  z-index: 1001;
  overflow-y: auto;
}

.side-panel.open {
  right: 0;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
  background: #f8f9fa;
}

.panel-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.2em;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #333;
  background: rgba(0,0,0,0.1);
  border-radius: 50%;
}

.panel-content {
  padding: 20px;
}

/* 来源统计概览 */
.source-overview {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 24px;
}

.overview-item {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
}

.overview-label {
  display: block;
  font-size: 0.8em;
  color: #666;
  margin-bottom: 4px;
}

.overview-value {
  display: block;
  font-size: 1.4em;
  font-weight: bold;
  color: #333;
}

/* 工具贡献列表 */
.tool-contributions h4 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 1.1em;
}

.contribution-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 12px;
  overflow: hidden;
}

.contribution-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  cursor: pointer;
  transition: background 0.2s ease;
}

.contribution-header:hover {
  background: #e9ecef;
}

.tool-icon {
  font-size: 1.2em;
  margin-right: 8px;
}

.tool-name {
  flex: 1;
  font-weight: 500;
}

.contribution-count {
  font-size: 0.9em;
  color: #666;
  margin-right: 8px;
}

.expand-icon {
  transition: transform 0.2s ease;
  color: #666;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.contribution-details {
  padding: 16px;
  border-top: 1px solid #e0e0e0;
}

.contribution-summary {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 16px;
}

.summary-item {
  font-size: 0.9em;
  color: #666;
}

.contributed-elements {
  margin-bottom: 16px;
}

.element-item {
  background: #f8f9fa;
  padding: 8px 12px;
  border-radius: 6px;
  margin-bottom: 8px;
}

.element-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.element-type {
  font-size: 0.8em;
  font-weight: 500;
  color: #333;
}

.element-confidence {
  font-size: 0.7em;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: bold;
}

.confidence-high {
  background-color: #4caf50;
  color: white;
}

.confidence-medium {
  background-color: #ff9800;
  color: white;
}

.confidence-low {
  background-color: #f44336;
  color: white;
}

.element-preview {
  font-size: 0.8em;
  color: #666;
  line-height: 1.3;
}

.raw-data-btn {
  background: #2196f3;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9em;
  transition: background 0.2s ease;
}

.raw-data-btn:hover {
  background: #1976d2;
}

/* AI内容统计 */
.ai-content-section {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
}

.ai-content-section h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 1.1em;
}

.ai-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.stat-item {
  background: #f3e5f5;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.9em;
}

.stat-label {
  color: #666;
}

.stat-value {
  font-weight: bold;
  color: #7b1fa2;
}

/* 原始数据弹窗 */
.raw-data-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  max-width: 80%;
  max-height: 80%;
  width: 600px;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.modal-body {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
}

.raw-data-content {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.8em;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
  margin: 0;
}

/* 遮罩层 */
.panel-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.3);
  z-index: 999;
}
</style>
