#!/usr/bin/env python3
"""
模拟结构化响应测试 - 直接测试前端渲染
通过WebSocket发送模拟的结构化响应来测试表格渲染
"""

import asyncio
import websockets
import json
import time

async def send_structured_response():
    """发送模拟的结构化响应到前端"""
    
    # 模拟AI生成的包含表格的结构化响应
    structured_content = {
        "type": "structured_response",
        "content": [
            {
                "type": "heading",
                "content": "🔧 风机叶片故障分析报告",
                "level": 1
            },
            {
                "type": "paragraph",
                "content": "根据您的查询，我为您分析了风机叶片的故障情况。以下是详细的故障统计数据："
            },
            {
                "type": "table",
                "headers": ["故障类型", "发生次数", "严重程度", "处理状态", "预计修复时间"],
                "rows": [
                    ["叶片裂纹", "15", "高", "已处理", "2-3天"],
                    ["表面磨损", "8", "中", "处理中", "1-2天"],
                    ["螺栓松动", "3", "低", "待处理", "半天"],
                    ["涂层脱落", "12", "中", "已处理", "1天"],
                    ["边缘损伤", "6", "高", "处理中", "3-4天"]
                ]
            },
            {
                "type": "heading",
                "content": "📊 故障分析",
                "level": 2
            },
            {
                "type": "paragraph",
                "content": "从统计数据可以看出，**叶片裂纹**是最常见的故障类型，占总故障的34%。这类故障的严重程度较高，需要优先处理。"
            },
            {
                "type": "heading",
                "content": "🔧 维护建议",
                "level": 2
            },
            {
                "type": "list",
                "list_type": "ordered",
                "items": [
                    "立即处理高严重程度故障（叶片裂纹、边缘损伤）",
                    "制定预防性维护计划，定期检查螺栓紧固情况",
                    "加强叶片表面涂层保护，延长使用寿命",
                    "建立故障预警系统，及时发现潜在问题"
                ]
            },
            {
                "type": "paragraph",
                "content": "希望这个分析对您的风机维护工作有所帮助！如有其他问题，请随时咨询。"
            }
        ],
        "raw_markdown": "# 🔧 风机叶片故障分析报告\n\n根据您的查询...",
        "timestamp": time.time()
    }

    # 包装为最终响应消息
    structured_response = {
        "type": "final_response",
        "content": structured_content,
        "session_id": "client_1",
        "timestamp": time.time()
    }
    
    try:
        # 连接到WebSocket服务器
        uri = "ws://localhost:8000/ws"
        async with websockets.connect(uri) as websocket:
            print("🔗 已连接到WebSocket服务器")
            
            # 发送结构化响应
            await websocket.send(json.dumps(structured_response))
            print("📤 已发送结构化响应")
            print(f"   📊 包含 {len(structured_response['content']['content'])} 个内容元素")
            
            # 检查每个元素类型
            for i, element in enumerate(structured_response['content']['content']):
                print(f"   元素{i+1}: {element['type']}")
                if element['type'] == 'table':
                    print(f"      表格: {len(element['headers'])} 列 x {len(element['rows'])} 行")
            
            print("✅ 结构化响应发送完成")
            
    except Exception as e:
        print(f"❌ 发送失败: {e}")

async def main():
    """主函数"""
    print("🧪 模拟结构化响应测试")
    print("=" * 50)
    
    print("📋 准备发送包含表格的结构化响应...")
    await send_structured_response()
    
    print("\n🎯 测试完成！请检查前端页面的表格渲染效果")

if __name__ == "__main__":
    asyncio.run(main())
