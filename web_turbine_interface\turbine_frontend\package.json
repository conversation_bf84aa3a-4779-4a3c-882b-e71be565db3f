{"name": "turbine-frontend", "version": "1.0.0", "description": "风机智能体 Web前端界面", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "serve": "vite preview --port 3000", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "axios": "^1.6.2", "element-plus": "^2.4.4", "highlight.js": "^11.9.0", "katex": "^0.16.22", "marked": "^11.1.1", "pinia": "^2.1.7", "socket.io-client": "^4.7.4", "vue": "^3.3.8", "vue-router": "^4.2.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "@vue/eslint-config-prettier": "^8.0.0", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.1", "prettier": "^3.1.0", "sass": "^1.69.5", "vite": "^5.0.0"}, "keywords": ["vue", "<PERSON><PERSON><PERSON>", "websocket", "wind-turbine", "智能体"], "author": "Wind Turbine Expert", "license": "MIT", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}