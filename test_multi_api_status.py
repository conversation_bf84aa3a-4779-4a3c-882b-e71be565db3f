#!/usr/bin/env python3
"""
测试四重API系统的状态和可用性
"""

import asyncio
import sys
import os
import time

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from multi_api_client import MultiAPIClient
from real_ai_client import RealAIClient
from simple_gemini_client import SimpleGeminiClient
from modelscope_client import ModelScopeClient

async def test_single_api(client, api_name, test_query="你好，请简单回复一下"):
    """测试单个API的可用性"""
    print(f"\n🧪 测试 {api_name} API...")

    try:
        start_time = time.time()

        if hasattr(client, 'chat_completion'):
            # 使用chat_completion方法
            messages = [{"role": "user", "content": test_query}]
            response = await client.chat_completion(messages)

            end_time = time.time()
            response_time = end_time - start_time

            if response and response.get('success') and response.get('content'):
                print(f"   ✅ {api_name}: 响应成功 ({response_time:.2f}s)")
                print(f"   📝 响应内容: {response['content'][:100]}...")
                return True
            else:
                print(f"   ❌ {api_name}: 响应失败 - {response.get('error', '未知错误')}")
                return False
        elif hasattr(client, 'test_connection'):
            # 使用test_connection方法
            response = await client.test_connection()

            end_time = time.time()
            response_time = end_time - start_time

            if response and response.get('success'):
                print(f"   ✅ {api_name}: 连接测试成功 ({response_time:.2f}s)")
                return True
            else:
                print(f"   ❌ {api_name}: 连接测试失败 - {response.get('error', '未知错误')}")
                return False
        else:
            print(f"   ❌ {api_name}: 未找到合适的测试方法")
            return False

    except Exception as e:
        print(f"   ❌ {api_name}: 测试失败 - {str(e)}")
        return False

async def test_multi_api_client():
    """测试MultiAPIClient的智能切换功能"""
    print(f"\n🔄 测试MultiAPIClient智能切换...")

    try:
        multi_client = MultiAPIClient()

        # 首先测试所有API的连接状态
        print(f"   🧪 测试所有API连接状态...")
        api_test_results = await multi_client.test_all_apis()

        # 测试基本响应
        test_query = "请生成一个简单的风机故障表格，包含故障类型和处理状态两列，3行数据即可"

        print(f"   📤 发送测试查询: {test_query}")

        # 格式化消息
        messages = multi_client.format_messages("你是一个风机专家", test_query)

        start_time = time.time()
        response = await multi_client.chat_completion_with_fallback(
            messages,
            original_query=test_query,
            stream=False
        )
        end_time = time.time()

        response_time = end_time - start_time

        if response and response.get('content'):
            print(f"   ✅ MultiAPIClient: 响应成功 ({response_time:.2f}s)")
            print(f"   🎯 当前使用API: {multi_client.current_api}")
            print(f"   📝 响应内容: {response['content'][:200]}...")

            # 显示API健康状态
            print(f"   📊 API健康状态:")
            for api_name, health in multi_client.api_health.items():
                status_emoji = "✅" if health["status"] == "healthy" else "❌" if health["status"] == "failed" else "❓"
                print(f"      {status_emoji} {api_name}: {health['status']} (失败次数: {health['failure_count']})")

            return True
        else:
            print(f"   ❌ MultiAPIClient: 响应为空或格式错误")
            print(f"   📝 原始响应: {response}")
            return False

    except Exception as e:
        print(f"   ❌ MultiAPIClient: 测试失败 - {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 四重API系统状态测试")
    print("=" * 60)
    
    # 测试结果统计
    results = {}
    
    # 1. 测试百炼API (qwen)
    try:
        qwen_client = RealAIClient()
        results['qwen'] = await test_single_api(qwen_client, "百炼(qwen)")
    except Exception as e:
        print(f"   ❌ 百炼API初始化失败: {e}")
        results['qwen'] = False
    
    # 2. 测试Gemini API
    try:
        gemini_client = SimpleGeminiClient()
        results['gemini'] = await test_single_api(gemini_client, "Gemini")
    except Exception as e:
        print(f"   ❌ Gemini API初始化失败: {e}")
        results['gemini'] = False
    
    # 3. 测试ModelScope API
    try:
        modelscope_client = ModelScopeClient()
        results['modelscope'] = await test_single_api(modelscope_client, "ModelScope")
    except Exception as e:
        print(f"   ❌ ModelScope API初始化失败: {e}")
        results['modelscope'] = False
    
    # 4. 测试MultiAPIClient智能切换
    results['multi_client'] = await test_multi_api_client()
    
    # 输出测试总结
    print(f"\n📊 测试结果总结")
    print("=" * 60)
    
    available_apis = []
    for api_name, status in results.items():
        status_emoji = "✅" if status else "❌"
        print(f"{status_emoji} {api_name}: {'可用' if status else '不可用'}")
        if status and api_name != 'multi_client':
            available_apis.append(api_name)
    
    print(f"\n🎯 可用API数量: {len(available_apis)}/{len(results)-1}")
    
    if available_apis:
        print(f"✅ 推荐使用的API: {available_apis[0]}")
        print(f"🔄 备用API: {available_apis[1:] if len(available_apis) > 1 else '无'}")
    else:
        print("❌ 所有API都不可用，请检查网络连接和API配置")
    
    # 如果MultiAPIClient可用，说明至少有一个API工作正常
    if results.get('multi_client', False):
        print(f"\n🎉 MultiAPIClient智能切换系统正常工作！")
        print(f"💡 建议: 继续使用MultiAPIClient，它会自动选择最佳可用的API")
    else:
        print(f"\n⚠️  MultiAPIClient出现问题，可能需要检查配置")

if __name__ == "__main__":
    asyncio.run(main())
