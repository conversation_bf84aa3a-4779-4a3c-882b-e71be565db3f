#!/usr/bin/env python3
"""
测试数学公式解析功能
"""

import sys
import os

# 添加项目路径
project_root = os.path.abspath(os.path.dirname(__file__))
backend_path = os.path.join(project_root, 'web_turbine_interface', 'turbine_backend')
sys.path.insert(0, backend_path)

from app.services.content_structure_parser import ContentStructureParser

def test_math_formula_parsing():
    """测试数学公式解析"""
    parser = ContentStructureParser()

    # 测试包含数学公式的文本
    test_text = """
当然可以，以下是一个包含数学公式渲染的专业示例：

在风机动力学分析中，经常会遇到系统振动的二阶微分方程模型。例如，一个简化的单自由度叶片振动方程可表示为：

求解此类方程时，其特征方程常表现为一个二次方程：$x^2 + 2x + 1 = 0$

此外，从能量角度分析，根据爱因斯坦质能方程，能量与质量的关系可表达为著名的块级公式：

$$E = mc^2$$

虽然该公式在风机设计中不直接应用，但在涉及高能物理或材料能量密度评估时，仍具有理论意义。
"""

    # 测试正则表达式
    import re
    math_block_pattern = re.compile(r'\$\$(.*?)\$\$', re.DOTALL)
    math_inline_pattern = re.compile(r'\$([^$\n]+)\$')

    print("🔍 测试正则表达式匹配:")
    block_matches = math_block_pattern.findall(test_text)
    inline_matches = math_inline_pattern.findall(test_text)

    print(f"   块级公式匹配: {block_matches}")
    print(f"   行内公式匹配: {inline_matches}")
    
    print("🧪 测试数学公式解析...")
    print(f"📝 输入文本:\n{test_text}")
    print("\n" + "="*50)
    
    # 直接测试数学公式提取
    print("\n🧮 直接测试数学公式提取:")
    math_formulas = parser._extract_math_formulas(test_text)
    print(f"   直接提取结果: {math_formulas}")

    # 解析结构化内容
    structured_content = parser.parse_markdown_to_structure(test_text)
    
    print(f"📊 解析结果: {len(structured_content)} 个元素")

    for i, element in enumerate(structured_content):
        print(f"\n🔸 元素 {i+1}:")
        print(f"   类型: {element['type']}")
        if element['type'] == 'math':
            print(f"   公式: {element['formula']}")
            print(f"   显示模式: {element['display']}")
        elif element['type'] == 'paragraph':
            print(f"   内容: {element['content'][:100]}...")
            # 检查段落中是否包含数学公式
            if '$' in element['content']:
                print(f"   ⚠️  段落包含$符号: {element['content']}")
        else:
            print(f"   内容: {str(element)[:100]}...")
    
    # 检查是否找到了数学公式
    math_elements = [e for e in structured_content if e['type'] == 'math']
    print(f"\n🧮 找到数学公式: {len(math_elements)} 个")
    
    for math_elem in math_elements:
        print(f"   - {math_elem['display']}: {math_elem['formula']}")
    
    return structured_content

if __name__ == "__main__":
    test_math_formula_parsing()
